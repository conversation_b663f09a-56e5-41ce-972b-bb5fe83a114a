<template>
  <!-- Overlay -->
  <div
    v-if="cartStore.isOpen"
    class="fixed inset-0 bg-black bg-opacity-50 z-40"
    @click="cartStore.closeCart()"
  />

  <!-- Cart Drawer -->
  <div class="cart-drawer" :class="cartStore.isOpen ? 'open' : 'closed'">
    <!-- Header -->
    <div class="flex items-center justify-between p-6 border-b border-gray-200">
      <h2 class="text-lg font-semibold text-gray-900">
        Shopping Cart ({{ cartStore.itemCount }})
      </h2>
      <button
        @click="cartStore.closeCart()"
        class="text-gray-400 hover:text-gray-600"
      >
        <UIcon name="i-heroicons-x-mark" class="w-6 h-6" />
      </button>
    </div>

    <!-- Cart Content -->
    <div class="flex-1 overflow-y-auto custom-scrollbar">
      <!-- Loading state -->
      <div
        v-if="cartStore.isLoading"
        class="flex justify-center items-center h-32"
      >
        <div class="loading-spinner" />
      </div>

      <!-- Empty cart -->
      <div
        v-else-if="cartStore.isEmpty"
        class="flex flex-col items-center justify-center h-64 text-center p-6"
      >
        <UIcon
          name="i-heroicons-shopping-bag"
          class="w-16 h-16 text-gray-400 mb-4"
        />
        <h3 class="text-lg font-medium text-gray-900 mb-2">
          Your cart is empty
        </h3>
        <p class="text-gray-500 mb-6">Add some products to get started</p>
        <button
          @click="cartStore.closeCart()"
          class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          Continue Shopping
        </button>
      </div>

      <!-- Cart items -->
      <div v-else class="p-6 space-y-4">
        <div
          v-for="item in cartStore.items"
          :key="item.id"
          class="flex items-center space-x-4 bg-gray-50 rounded-lg p-4"
        >
          <!-- Product Image -->
          <div class="flex-shrink-0">
            <img
              v-if="item.product.images && item.product.images.length > 0"
              :src="item.product.images[0].url"
              :alt="item.product.name"
              class="w-16 h-16 object-cover rounded-lg"
            />
            <div
              v-else
              class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center"
            >
              <UIcon name="i-heroicons-photo" class="w-6 h-6 text-gray-400" />
            </div>
          </div>

          <!-- Product Info -->
          <div class="flex-1 min-w-0">
            <h4 class="text-sm font-medium text-gray-900 truncate">
              {{ item.product.name }}
            </h4>
            <p v-if="item.variant" class="text-sm text-gray-500">
              {{ item.variant.name }}: {{ item.variant.value }}
            </p>
            <div class="flex items-center justify-between mt-2">
              <span class="text-sm font-medium text-gray-900">
                ${{ cartStore.getItemPrice(item).toFixed(2) }}
              </span>

              <!-- Quantity controls -->
              <div class="flex items-center space-x-2">
                <button
                  @click="updateQuantity(item.id, item.quantity - 1)"
                  :disabled="cartStore.isLoading"
                  class="w-8 h-8 flex items-center justify-center rounded-full border border-gray-300 hover:bg-gray-100 disabled:opacity-50"
                >
                  <UIcon name="i-heroicons-minus" class="w-4 h-4" />
                </button>

                <span class="w-8 text-center text-sm font-medium">
                  {{ item.quantity }}
                </span>

                <button
                  @click="updateQuantity(item.id, item.quantity + 1)"
                  :disabled="cartStore.isLoading"
                  class="w-8 h-8 flex items-center justify-center rounded-full border border-gray-300 hover:bg-gray-100 disabled:opacity-50"
                >
                  <UIcon name="i-heroicons-plus" class="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>

          <!-- Remove button -->
          <button
            @click="removeItem(item.id)"
            :disabled="cartStore.isLoading"
            class="text-red-500 hover:text-red-700 disabled:opacity-50"
          >
            <UIcon name="i-heroicons-trash" class="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div
      v-if="!cartStore.isEmpty"
      class="border-t border-gray-200 p-6 space-y-4"
    >
      <!-- Subtotal -->
      <div class="flex items-center justify-between text-lg font-semibold">
        <span>Subtotal:</span>
        <span>${{ cartStore.subtotal.toFixed(2) }}</span>
      </div>

      <!-- Shipping note -->
      <p class="text-sm text-gray-500 text-center">
        Shipping and taxes calculated at checkout
      </p>

      <!-- Action buttons -->
      <div class="space-y-3">
        <NuxtLink
          to="/cart"
          @click="cartStore.closeCart()"
          class="block w-full bg-gray-100 text-gray-900 text-center py-3 rounded-lg font-medium hover:bg-gray-200 transition-colors"
        >
          View Cart
        </NuxtLink>

        <NuxtLink
          to="/checkout"
          @click="cartStore.closeCart()"
          class="block w-full bg-blue-600 text-white text-center py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
        >
          Checkout
        </NuxtLink>
      </div>

      <!-- Clear cart -->
      <button
        @click="clearCart"
        :disabled="cartStore.isLoading"
        class="w-full text-sm text-red-600 hover:text-red-800 disabled:opacity-50"
      >
        Clear Cart
      </button>
    </div>
  </div>
</template>

<script setup>
const cartStore = useCartStore();

const updateQuantity = async (itemId, quantity) => {
  if (quantity <= 0) {
    await removeItem(itemId);
  } else {
    await cartStore.updateQuantity(itemId, quantity);
  }
};

const removeItem = async (itemId) => {
  await cartStore.removeItem(itemId);
};

const clearCart = async () => {
  if (confirm("Are you sure you want to clear your cart?")) {
    await cartStore.clearCart();
  }
};
</script>
