{"name": "clear-module", "version": "4.1.2", "description": "Clear a module from the cache", "license": "MIT", "repository": "sindresorhus/clear-module", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["clear", "module", "require", "import", "cache", "uncache", "uncached", "unrequire", "derequire", "delete", "remove", "rm", "fresh"], "dependencies": {"parent-module": "^2.0.0", "resolve-from": "^5.0.0"}, "devDependencies": {"ava": "^2.1.0", "tsd": "^0.7.2", "xo": "^0.24.0"}}