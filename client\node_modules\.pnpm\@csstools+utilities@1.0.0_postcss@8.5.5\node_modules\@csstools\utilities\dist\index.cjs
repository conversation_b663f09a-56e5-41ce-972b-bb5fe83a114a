"use strict";exports.hasFallback=function hasFallback(t){const e=t.parent;if(!e)return!1;const r=t.prop.toLowerCase(),s=e.index(t);for(let t=0;t<s;t++){const s=e.nodes[t];if("decl"===s.type&&s.prop.toLowerCase()===r)return!0}return!1},exports.hasSupportsAtRuleAncestor=function hasSupportsAtRuleAncestor(t,e){let r=t.parent;for(;r;)if("atrule"===r.type&&"supports"===r.name.toLowerCase()){if(e.test(r.params))return!0;r=r.parent}else r=r.parent;return!1};
