// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: "2025-05-15",
  devtools: { enabled: true },

  // Development server configuration
  devServer: {
    port: 3002,
    host: "localhost",
  },

  modules: [
    "@nuxt/eslint",
    "@nuxt/fonts",
    "@nuxt/icon",
    "@nuxt/image",
    "@nuxtjs/tailwindcss",
    "@nuxt/ui",
    "@pinia/nuxt",
    "@nuxtjs/i18n",
    "@vueuse/nuxt",
  ],

  // Runtime config
  runtimeConfig: {
    public: {
      apiBase: process.env.NUXT_PUBLIC_API_BASE || "http://localhost:3001",
      stripePublishableKey:
        process.env.NUXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || "",
    },
  },

  // CSS configuration
  css: ["~/assets/css/main.css"],

  // Pinia configuration
  pinia: {
    storesDirs: ["./stores/**"],
  },

  // i18n configuration
  i18n: {
    locales: [
      { code: "en", name: "English", file: "en.json" },
      { code: "et", name: "Eesti", file: "et.json" },
    ],
    defaultLocale: "en",
    langDir: "./locales/",
    strategy: "prefix_except_default",
  },

  // Image optimization
  image: {
    quality: 80,
    format: ["webp", "jpg"],
    screens: {
      xs: 320,
      sm: 640,
      md: 768,
      lg: 1024,
      xl: 1280,
      xxl: 1536,
    },
  },

  // App configuration
  app: {
    head: {
      title: "Ecommerce Store",
      meta: [
        { charset: "utf-8" },
        { name: "viewport", content: "width=device-width, initial-scale=1" },
        {
          name: "description",
          content: "Beautiful ecommerce webstore with modern design",
        },
      ],
      link: [{ rel: "icon", type: "image/x-icon", href: "/favicon.ico" }],
    },
  },

  // Tailwind CSS
  tailwindcss: {
    configPath: "tailwind.config.js",
  },

  // Build configuration
  build: {
    transpile: ["@stripe/stripe-js"],
  },
});
