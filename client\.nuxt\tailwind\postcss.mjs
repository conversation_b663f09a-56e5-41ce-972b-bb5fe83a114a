// generated by the @nuxtjs/tailwindcss <https://github.com/nuxt-modules/tailwindcss> module at 16.6.2025, 13:32:22
import "@nuxtjs/tailwindcss/config-ctx"
import configMerger from "@nuxtjs/tailwindcss/merger";

import cfg2 from "./../../tailwind.config.js";
import cfg3 from "./../../tailwind.config.js";
const config = [
{"content":{"files":["C:/Users/<USER>/OneDrive/Töölaud/ecommerce/client/components/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/OneDrive/Töölaud/ecommerce/client/components/global/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/OneDrive/Töölaud/ecommerce/client/components/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/OneDrive/Töölaud/ecommerce/client/layouts/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/OneDrive/Töölaud/ecommerce/client/plugins/**/*.{js,ts,mjs}","C:/Users/<USER>/OneDrive/Töölaud/ecommerce/client/composables/**/*.{js,ts,mjs}","C:/Users/<USER>/OneDrive/Töölaud/ecommerce/client/utils/**/*.{js,ts,mjs}","C:/Users/<USER>/OneDrive/Töölaud/ecommerce/client/pages/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/OneDrive/Töölaud/ecommerce/client/{A,a}pp.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/OneDrive/Töölaud/ecommerce/client/{E,e}rror.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/OneDrive/Töölaud/ecommerce/client/app.config.{js,ts,mjs}"]}},
{},
cfg2,
cfg3
].reduce((acc, curr) => configMerger(acc, curr), {});

const resolvedConfig = (() => {const cfg=config;cfg["darkMode"] = ["selector","[class~=\"dark\"]"];;return cfg;})();

export default resolvedConfig;