<template>
  <div>
    <!-- Hero Slideshow -->
    <section class="relative">
      <div class="slideshow-container">
        <div
          v-if="
            siteStore.activeSlideshows && siteStore.activeSlideshows.length > 0
          "
          class="relative h-full"
        >
          <div
            v-for="(slide, index) in siteStore.activeSlideshows"
            :key="slide.id"
            v-show="currentSlide === index"
            class="absolute inset-0"
          >
            <img
              :src="slide.image"
              :alt="slide.title"
              class="w-full h-full object-cover"
            />
            <div class="slide-content">
              <div class="max-w-4xl mx-auto text-center container-padding">
                <h1
                  v-if="slide.title"
                  class="slide-title"
                  :style="slide.styles?.title"
                >
                  {{ slide.title }}
                </h1>
                <p
                  v-if="slide.subtitle"
                  class="slide-subtitle"
                  :style="slide.styles?.subtitle"
                >
                  {{ slide.subtitle }}
                </p>
                <div
                  v-if="slide.content"
                  class="text-lg mb-8 opacity-90"
                  v-html="slide.content"
                />
                <NuxtLink
                  v-if="slide.link && slide.buttonText"
                  :to="slide.link"
                  class="inline-block bg-white text-gray-900 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors shadow-lg"
                >
                  {{ slide.buttonText }}
                </NuxtLink>
              </div>
            </div>
          </div>

          <!-- Slideshow controls -->
          <div
            v-if="
              siteStore.activeSlideshows &&
              siteStore.activeSlideshows.length > 1
            "
            class="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2"
          >
            <button
              v-for="(slide, index) in siteStore.activeSlideshows"
              :key="index"
              @click="currentSlide = index"
              class="w-3 h-3 rounded-full transition-colors"
              :class="currentSlide === index ? 'bg-white' : 'bg-white/50'"
            />
          </div>

          <!-- Navigation arrows -->
          <button
            v-if="
              siteStore.activeSlideshows &&
              siteStore.activeSlideshows.length > 1
            "
            @click="prevSlide"
            class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-2 rounded-full transition-colors"
          >
            <UIcon name="i-heroicons-chevron-left" class="w-6 h-6" />
          </button>
          <button
            v-if="
              siteStore.activeSlideshows &&
              siteStore.activeSlideshows.length > 1
            "
            @click="nextSlide"
            class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-2 rounded-full transition-colors"
          >
            <UIcon name="i-heroicons-chevron-right" class="w-6 h-6" />
          </button>
        </div>

        <!-- Default hero if no slideshows -->
        <div v-else class="slide-content">
          <div class="max-w-4xl mx-auto text-center container-padding">
            <h1 class="slide-title">Welcome to Our Store</h1>
            <p class="slide-subtitle">
              Discover amazing products at great prices
            </p>
            <NuxtLink
              to="/products"
              class="inline-block bg-white text-gray-900 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors shadow-lg"
            >
              Shop Now
            </NuxtLink>
          </div>
        </div>
      </div>
    </section>

    <!-- Main content area -->
    <div class="container mx-auto container-padding section-padding">
      <div class="flex flex-col lg:flex-row gap-8">
        <!-- Category Sidebar -->
        <aside class="lg:w-1/4">
          <div class="category-sidebar">
            <h2 class="text-xl font-semibold mb-6 text-gray-900">Categories</h2>
            <nav class="space-y-1">
              <NuxtLink
                to="/products"
                class="category-item"
                :class="{ active: !selectedCategory }"
              >
                All Products
              </NuxtLink>
              <NuxtLink
                v-for="category in categoriesStore.navigationCategories"
                :key="category.id"
                :to="`/products?category=${category.slug}`"
                class="category-item"
                :class="{ active: selectedCategory === category.slug }"
              >
                <div class="flex items-center justify-between">
                  <span>{{ category.name }}</span>
                  <span
                    v-if="category._count?.products"
                    class="text-sm text-gray-500"
                  >
                    {{ category._count.products }}
                  </span>
                </div>

                <!-- Subcategories -->
                <div
                  v-if="category.children && category.children.length > 0"
                  class="ml-4 mt-2 space-y-1"
                >
                  <NuxtLink
                    v-for="child in category.children"
                    :key="child.id"
                    :to="`/products?category=${child.slug}`"
                    class="block py-2 px-3 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded"
                  >
                    {{ child.name }}
                  </NuxtLink>
                </div>
              </NuxtLink>
            </nav>
          </div>
        </aside>

        <!-- Products Grid -->
        <main class="lg:w-3/4">
          <!-- Featured Products -->
          <section
            v-if="productsStore.featuredProducts.length > 0"
            class="mb-12"
          >
            <h2 class="text-2xl font-bold text-gray-900 mb-8">
              Featured Products
            </h2>
            <div class="product-grid">
              <ProductCard
                v-for="product in productsStore.featuredProducts"
                :key="product.id"
                :product="product"
                class="fade-in"
              />
            </div>
          </section>

          <!-- All Products -->
          <section>
            <div class="flex items-center justify-between mb-8">
              <h2 class="text-2xl font-bold text-gray-900">
                {{ selectedCategory ? "Category Products" : "All Products" }}
              </h2>

              <!-- Sort dropdown -->
              <USelect
                v-model="sortBy"
                :options="sortOptions"
                placeholder="Sort by"
                class="w-48"
                @change="handleSortChange"
              />
            </div>

            <!-- Loading state -->
            <div
              v-if="productsStore.isLoading"
              class="flex justify-center py-12"
            >
              <div class="loading-spinner" />
            </div>

            <!-- Products grid -->
            <div
              v-else-if="productsStore.products.length > 0"
              class="product-grid"
            >
              <ProductCard
                v-for="product in productsStore.products"
                :key="product.id"
                :product="product"
                class="fade-in"
              />
            </div>

            <!-- Empty state -->
            <div v-else class="text-center py-12">
              <UIcon
                name="i-heroicons-shopping-bag"
                class="w-16 h-16 text-gray-400 mx-auto mb-4"
              />
              <h3 class="text-lg font-medium text-gray-900 mb-2">
                No products found
              </h3>
              <p class="text-gray-500">
                Try adjusting your filters or browse all categories.
              </p>
            </div>

            <!-- Pagination -->
            <div
              v-if="productsStore.pagination.totalPages > 1"
              class="flex justify-center mt-12"
            >
              <UPagination
                v-model="currentPage"
                :total="productsStore.pagination.total"
                :page-count="productsStore.pagination.limit"
                @update:model-value="handlePageChange"
              />
            </div>
          </section>
        </main>
      </div>
    </div>
  </div>
</template>

<script setup>
const siteStore = useSiteStore();
const productsStore = useProductsStore();
const categoriesStore = useCategoriesStore();
const route = useRoute();

// Reactive data
const currentSlide = ref(0);
const selectedCategory = ref(route.query.category || "");
const currentPage = ref(1);
const sortBy = ref("createdAt-desc");

// Sort options
const sortOptions = [
  { label: "Newest First", value: "createdAt-desc" },
  { label: "Oldest First", value: "createdAt-asc" },
  { label: "Price: Low to High", value: "price-asc" },
  { label: "Price: High to Low", value: "price-desc" },
  { label: "Name: A to Z", value: "name-asc" },
  { label: "Name: Z to A", value: "name-desc" },
];

// Slideshow auto-play
let slideInterval = null;

const startSlideshow = () => {
  const slideshows = siteStore.activeSlideshows || [];
  if (slideshows.length > 1) {
    slideInterval = setInterval(() => {
      nextSlide();
    }, 5000);
  }
};

const stopSlideshow = () => {
  if (slideInterval) {
    clearInterval(slideInterval);
    slideInterval = null;
  }
};

const nextSlide = () => {
  const slideshows = siteStore.activeSlideshows || [];
  if (slideshows.length > 0) {
    currentSlide.value = (currentSlide.value + 1) % slideshows.length;
  }
};

const prevSlide = () => {
  const slideshows = siteStore.activeSlideshows || [];
  if (slideshows.length > 0) {
    currentSlide.value =
      currentSlide.value === 0 ? slideshows.length - 1 : currentSlide.value - 1;
  }
};

// Handlers
const handleSortChange = () => {
  const [sortField, sortOrder] = sortBy.value.split("-");
  fetchProducts({
    sortBy: sortField,
    sortOrder,
    page: 1,
  });
};

const handlePageChange = (page) => {
  currentPage.value = page;
  fetchProducts({ page });
};

const fetchProducts = async (params = {}) => {
  const [sortField, sortOrder] = sortBy.value.split("-");

  await productsStore.fetchProducts({
    page: currentPage.value,
    category: selectedCategory.value,
    sortBy: sortField,
    sortOrder,
    ...params,
  });
};

// Watch for route changes
watch(
  () => route.query.category,
  (newCategory) => {
    selectedCategory.value = newCategory || "";
    currentPage.value = 1;
    fetchProducts({ page: 1 });
  }
);

// Lifecycle
onMounted(async () => {
  // Initialize data
  await Promise.all([
    categoriesStore.initializeCategories(),
    productsStore.fetchFeaturedProducts(),
    fetchProducts(),
  ]);

  // Start slideshow
  startSlideshow();
});

onUnmounted(() => {
  stopSlideshow();
});

// Use simple layout to avoid component issues
definePageMeta({
  layout: "default-simple",
});

// SEO
useHead({
  title: "Home - Ecommerce Store",
  meta: [
    {
      name: "description",
      content:
        "Discover amazing products at great prices. Shop our featured collection and browse by category.",
    },
  ],
});
</script>
