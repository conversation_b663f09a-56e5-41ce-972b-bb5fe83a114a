{"name": "@nuxtjs/tailwindcss", "version": "6.11.4", "description": "TailwindCSS module for Nuxt", "repository": "nuxt-modules/tailwindcss", "license": "MIT", "type": "module", "configKey": "tailwindcss", "compatibility": {"nuxt": "^2.9.0 || ^3.0.0-rc.1"}, "exports": {".": {"types": "./dist/types.d.ts", "require": "./dist/module.cjs", "import": "./dist/module.mjs"}}, "main": "./dist/module.cjs", "types": "./dist/types.d.ts", "files": ["dist"], "dependencies": {"@nuxt/kit": "^3.9.3", "autoprefixer": "^10.4.17", "chokidar": "^3.5.3", "clear-module": "^4.1.2", "consola": "^3.2.3", "defu": "^6.1.4", "h3": "^1.10.0", "micromatch": "^4.0.5", "pathe": "^1.1.2", "postcss": "^8.4.33", "postcss-custom-properties": "^13.3.4", "postcss-nesting": "^12.0.2", "tailwind-config-viewer": "^1.7.3", "tailwindcss": "~3.4.1", "ufo": "^1.3.2"}, "devDependencies": {"@fontsource/inter": "^5.0.16", "@nuxt/content": "^2.10.0", "@nuxt/devtools": "^1.0.8", "@nuxt/eslint-config": "latest", "@nuxt/module-builder": "^0.5.5", "@nuxt/test-utils": "^3.10.0", "@tailwindcss/typography": "^0.5.10", "@types/micromatch": "^4.0.6", "changelogen": "^0.5.5", "destr": "^2.0.2", "eslint": "latest", "happy-dom": "^13.1.4", "nuxt": "^3.9.3", "typescript": "^5.3.3", "vitest": "1.1.0"}, "packageManager": "pnpm@8.14.1", "resolutions": {"@nuxtjs/tailwindcss": "link:."}, "stackblitz": {"startCommand": "pnpm dev:prepare && pnpm dev"}, "scripts": {"play": "pnpm dev", "dev": "nuxi dev playground", "dev:build": "nuxi build playground", "dev:generate": "nuxi generate playground", "dev:nuxt2": "nuxi dev nuxt2-playground", "dev:prepare": "pnpm prepare && pnpm build:stub", "build": "nuxt-module-build build", "build:stub": "pnpm build --stub", "release": "pnpm lint && pnpm test && pnpm prepack && pnpm changelogen --release --push && pnpm publish", "docs:build": "nuxi generate docs", "docs:preview": "nuxi preview docs", "docs:dev": "nuxi dev docs", "lint": "eslint .", "lint:fix": "pnpm lint --fix", "test": "vitest run", "test:watch": "vitest watch", "test:types": "pnpm dev:prepare && tsc --noEmit && nuxi typecheck playground"}}