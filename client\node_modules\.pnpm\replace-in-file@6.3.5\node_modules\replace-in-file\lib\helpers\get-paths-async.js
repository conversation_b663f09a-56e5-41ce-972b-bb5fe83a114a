'use strict';

/**
 * Dependencies
 */
const globAsync = require('./glob-async');

/**
 * Get paths asynchrously
 */
module.exports = function getPathsAsync(patterns, config) {

  //Extract relevant config
  const {ignore, disableGlobs, allowEmptyPaths, glob: cfg} = config;

  //Not using globs?
  if (disableGlobs) {
    return Promise.resolve(patterns);
  }

  //Expand globs and flatten paths
  return Promise
    .all(patterns
      .map(pattern => globAsync(pattern, ignore, allowEmptyPaths, cfg)))
    .then(paths => [].concat.apply([], paths));
};
