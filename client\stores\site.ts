import { defineStore } from "pinia";

interface Slideshow {
  id: string;
  title?: string;
  subtitle?: string;
  content?: string;
  image: string;
  link?: string;
  buttonText?: string;
  isActive: boolean;
  sortOrder: number;
  styles?: any;
  createdAt: string;
  updatedAt: string;
}

interface SiteSettings {
  logo?: string;
  siteName?: string;
  siteDescription?: string;
  contactEmail?: string;
  contactPhone?: string;
  address?: string;
  socialMedia?: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
  };
  notifications?: {
    enabled: boolean;
    message?: string;
  };
  gdpr?: {
    enabled: boolean;
    message?: string;
  };
}

interface SiteState {
  slideshows: Slideshow[];
  settings: SiteSettings;
  isLoading: boolean;
  showEmailPopup: boolean;
  showGdprBanner: boolean;
  showNotificationBar: boolean;
}

export const useSiteStore = defineStore("site", {
  state: (): SiteState => ({
    slideshows: [],
    settings: {},
    isLoading: false,
    showEmailPopup: false,
    showGdprBanner: false,
    showNotificationBar: false,
  }),

  getters: {
    activeSlideshows: (state) => {
      return state.slideshows
        .filter((slide) => slide.isActive)
        .sort((a, b) => a.sortOrder - b.sortOrder);
    },

    siteLogo: (state) => {
      return state.settings.logo || "/logo.svg";
    },

    siteName: (state) => {
      return state.settings.siteName || "Ecommerce Store";
    },

    shouldShowEmailPopup: (state) => {
      // Check if user has already seen the popup (localStorage)
      if (process.client) {
        const hasSeenPopup = localStorage.getItem("email-popup-seen");
        const hasSubscribed = localStorage.getItem("email-subscribed");
        return !hasSeenPopup && !hasSubscribed && state.showEmailPopup;
      }
      return false;
    },

    shouldShowGdprBanner: (state) => {
      // Check if user has accepted GDPR (localStorage)
      if (process.client) {
        const hasAcceptedGdpr = localStorage.getItem("gdpr-accepted");
        return !hasAcceptedGdpr && state.settings.gdpr?.enabled;
      }
      return false;
    },
  },

  actions: {
    async fetchSlideshows() {
      try {
        const { $api } = useNuxtApp();
        const response = await $api("/public/slideshows");
        this.slideshows = response.slideshows;
      } catch (error) {
        console.error("Fetch slideshows error:", error);
      }
    },

    async fetchSettings() {
      try {
        const { $api } = useNuxtApp();
        const response = await $api("/public/settings");
        this.settings = response.settings;
      } catch (error) {
        console.error("Fetch settings error:", error);
      }
    },

    async subscribeToNewsletter(email: string, source: string = "popup") {
      try {
        const { $api } = useNuxtApp();
        const response = await $api("/email/subscribe", {
          method: "POST",
          body: { email, source },
        });

        // Mark as subscribed in localStorage
        if (process.client) {
          localStorage.setItem("email-subscribed", "true");
          localStorage.setItem("email-popup-seen", "true");
        }

        this.showEmailPopup = false;

        const toast = useToast();
        toast.add({
          title: "Successfully subscribed!",
          description: `Use code ${response.discountCode} for 15% off your first order`,
          color: "green",
          timeout: 5000,
        });

        return response;
      } catch (error: any) {
        console.error("Subscribe error:", error);

        const toast = useToast();
        toast.add({
          title: "Subscription failed",
          description: error.data?.error || "Please try again",
          color: "red",
        });

        throw error;
      }
    },

    showEmailPopupAfterDelay(delay: number = 10000) {
      if (this.shouldShowEmailPopup) {
        setTimeout(() => {
          this.showEmailPopup = true;
        }, delay);
      }
    },

    hideEmailPopup() {
      this.showEmailPopup = false;

      // Mark as seen in localStorage
      if (process.client) {
        localStorage.setItem("email-popup-seen", "true");
      }
    },

    acceptGdpr() {
      this.showGdprBanner = false;

      // Mark as accepted in localStorage
      if (process.client) {
        localStorage.setItem("gdpr-accepted", "true");
      }
    },

    initializePopups() {
      // Initialize GDPR banner
      if (this.shouldShowGdprBanner) {
        this.showGdprBanner = true;
      }

      // Initialize email popup with delay
      this.showEmailPopupAfterDelay();

      // Initialize notification bar
      if (this.settings.notifications?.enabled) {
        this.showNotificationBar = true;
      }
    },

    async initializeSite() {
      this.isLoading = true;
      try {
        await Promise.all([this.fetchSlideshows(), this.fetchSettings()]);

        // Initialize popups after settings are loaded
        this.initializePopups();
      } catch (error) {
        console.error("Initialize site error:", error);
      } finally {
        this.isLoading = false;
      }
    },
  },
});
