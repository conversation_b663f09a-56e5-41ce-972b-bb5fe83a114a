import { join, relative, resolve, dirname, isAbsolute } from 'pathe';
import { watch } from 'chokidar';
import { useNuxt, tryResolveModule, findPath, resolve<PERSON><PERSON>s, useLogger, addTemplate, addTypeTemplate, isNuxt3, addDevServer<PERSON>and<PERSON>, isNuxt2, defineNuxtModule, createResolver, resolvePath, getNuxtVersion, installModule, addImports, addVitePlugin } from '@nuxt/kit';
import defaultTailwindConfig from 'tailwindcss/stubs/config.simple.js';
import resolveConfig from 'tailwindcss/resolveConfig.js';
import loadConfig from 'tailwindcss/loadConfig.js';
import { createDefu, defu } from 'defu';
import { existsSync } from 'fs';
import { LogLevels } from 'consola';
import micromatch from 'micromatch';
import { colors } from 'consola/utils';
import { eventHand<PERSON>, sendRedirect, H3Event } from 'h3';
import { joinURL, withTrailingSlash, withoutTrailingSlash, cleanDoubleSlashes } from 'ufo';

const NON_ALPHANUMERIC_RE = /^[0-9a-z]+$/i;
const isJSObject = (value) => typeof value === "object" && !Array.isArray(value);
const configMerger = createDefu((obj, key, value) => {
  if (key === "content") {
    if (isJSObject(obj[key]) && Array.isArray(value)) {
      obj[key]["files"] = [...obj[key]["files"] || [], ...value];
      return true;
    } else if (Array.isArray(obj[key]) && isJSObject(value)) {
      obj[key] = { ...value, files: [...obj[key], ...value.files || []] };
      return true;
    }
  }
  if (Array.isArray(obj[key]) && typeof value === "function") {
    obj[key] = value(obj[key]);
    return true;
  }
});

const resolveConfigPath = async (path) => Promise.all(
  (Array.isArray(path) ? path : [path]).filter(Boolean).map((path2) => findPath(path2, { extensions: [".js", ".cjs", ".mjs", ".ts"] }))
).then((paths) => paths.filter((p) => Boolean(p)));
const resolveContentPaths = (srcDir, nuxtOptions = useNuxt().options) => {
  const r = (p) => p.startsWith(srcDir) ? p : resolve(srcDir, p);
  const extensionFormat = (s) => s.length > 1 ? `.{${s.join(",")}}` : `.${s.join("") || "vue"}`;
  const defaultExtensions = extensionFormat(["js", "ts", "mjs"]);
  const sfcExtensions = extensionFormat(Array.from(/* @__PURE__ */ new Set([".vue", ...nuxtOptions.extensions])).map((e) => e.replace(/^\.*/, "")));
  const importDirs = [...nuxtOptions.imports?.dirs || []].map(r);
  const [composablesDir, utilsDir] = [resolve(srcDir, "composables"), resolve(srcDir, "utils")];
  if (!importDirs.includes(composablesDir))
    importDirs.push(composablesDir);
  if (!importDirs.includes(utilsDir))
    importDirs.push(utilsDir);
  return [
    r(`components/**/*${sfcExtensions}`),
    ...(() => {
      if (nuxtOptions.components) {
        return (Array.isArray(nuxtOptions.components) ? nuxtOptions.components : typeof nuxtOptions.components === "boolean" ? ["components"] : nuxtOptions.components.dirs).map((d) => `${resolveAlias(typeof d === "string" ? d : d.path)}/**/*${sfcExtensions}`);
      }
      return [];
    })(),
    nuxtOptions.dir.layouts && r(`${nuxtOptions.dir.layouts}/**/*${sfcExtensions}`),
    ...[true, void 0].includes(nuxtOptions.pages) ? [r(`${nuxtOptions.dir.pages}/**/*${sfcExtensions}`)] : [],
    nuxtOptions.dir.plugins && r(`${nuxtOptions.dir.plugins}/**/*${defaultExtensions}`),
    ...importDirs.map((d) => `${d}/**/*${defaultExtensions}`),
    r(`{A,a}pp${sfcExtensions}`),
    r(`{E,e}rror${sfcExtensions}`),
    r(`app.config${defaultExtensions}`)
  ].filter(Boolean);
};
const resolveModulePaths = async (configPath, nuxt = useNuxt()) => {
  const mainPaths = [await resolveConfigPath(configPath), resolveContentPaths(nuxt.options.srcDir, nuxt.options)];
  if (Array.isArray(nuxt.options._layers) && nuxt.options._layers.length > 1) {
    const layerPaths = await Promise.all(
      nuxt.options._layers.slice(1).map(async (layer) => [
        await resolveConfigPath(layer?.config?.tailwindcss?.configPath || join(layer.cwd, "tailwind.config")),
        resolveContentPaths(layer?.config?.srcDir || layer.cwd, defu(layer.config, nuxt.options))
      ])
    );
    layerPaths.forEach(([configPaths, contentPaths]) => {
      mainPaths[0].unshift(...configPaths);
      mainPaths[1].unshift(...contentPaths);
    });
  }
  return mainPaths;
};
async function resolveCSSPath(cssPath, nuxt = useNuxt()) {
  if (typeof cssPath === "string") {
    return existsSync(cssPath) ? [cssPath, `Using Tailwind CSS from ~/${relative(nuxt.options.srcDir, cssPath)}`] : await tryResolveModule("tailwindcss/package.json").then((twLocation) => twLocation ? [join(twLocation, "../tailwind.css"), "Using default Tailwind CSS file"] : Promise.reject("Unable to resolve tailwindcss. Is it installed?"));
  } else {
    return [
      cssPath && false,
      "No Tailwind CSS file found. Skipping..."
    ];
  }
}
const resolveBoolObj = (config, fb) => defu(typeof config === "object" ? config : {}, fb);
const resolveViewerConfig = (config) => resolveBoolObj(config, { endpoint: "/_tailwind", exportViewer: false });
const resolveExposeConfig = (config) => resolveBoolObj(config, { alias: "#tailwind-config", level: 2 });
const resolveEditorSupportConfig = (config) => resolveBoolObj(config, { autocompleteUtil: true, generateConfig: false });
function resolveInjectPosition(css, position = "first") {
  if (typeof position === "number") {
    return ~~Math.min(position, css.length + 1);
  }
  if (typeof position === "string") {
    switch (position) {
      case "first":
        return 0;
      case "last":
        return css.length;
      default:
        throw new Error("invalid literal: " + position);
    }
  }
  if (position.after !== void 0) {
    const index = css.indexOf(position.after);
    if (index === -1) {
      throw new Error("`after` position specifies a file which does not exists on CSS stack: " + position.after);
    }
    return index + 1;
  }
  throw new Error("invalid position: " + JSON.stringify(position));
}

const logger = useLogger("nuxt:tailwindcss");

function createTemplates(resolvedConfig, config, nuxt = useNuxt()) {
  const dtsContent = [];
  const populateMap = (obj, path = [], level = 1) => {
    Object.entries(obj).forEach(([key, value = {}]) => {
      const subpath = path.concat(key).join("/");
      if (level >= config.level || // if recursive call is more than desired
      !isJSObject(value) || // if its not an object, no more recursion required
      Object.keys(value).find((k) => !k.match(NON_ALPHANUMERIC_RE))) {
        if (isJSObject(value)) {
          const [validKeys, invalidKeys] = [[], []];
          Object.keys(value).forEach((i) => (NON_ALPHANUMERIC_RE.test(i) ? validKeys : invalidKeys).push(i));
          addTemplate({
            filename: `tailwind.config/${subpath}.mjs`,
            getContents: () => `${validKeys.map((i) => `const _${i} = ${JSON.stringify(value[i])}`).join("\n")}
const config = { ${validKeys.map((i) => `"${i}": _${i}, `).join("")}${invalidKeys.map((i) => `"${i}": ${JSON.stringify(value[i])}, `).join("")} }
export { config as default${validKeys.length > 0 ? ", _" : ""}${validKeys.join(", _")} }`,
            write: config.write
          });
          dtsContent.push(`declare module "${config.alias}/${subpath}" { ${validKeys.map((i) => `export const _${i}: ${JSON.stringify(value[i])};`).join("")} const defaultExport: { ${validKeys.map((i) => `"${i}": typeof _${i}, `).join("")}${invalidKeys.map((i) => `"${i}": ${JSON.stringify(value[i])}, `).join("")} }; export default defaultExport; }`);
        } else {
          addTemplate({
            filename: `tailwind.config/${subpath}.mjs`,
            getContents: () => `export default ${JSON.stringify(value, null, 2)}`,
            write: config.write
          });
          dtsContent.push(`declare module "${config.alias}/${subpath}" { const defaultExport: ${JSON.stringify(value)}; export default defaultExport; }`);
        }
      } else {
        populateMap(value, path.concat(key), level + 1);
        const values = Object.keys(value);
        addTemplate({
          filename: `tailwind.config/${subpath}.mjs`,
          getContents: () => `${values.map((v) => `import _${v} from "./${key}/${v}.mjs"`).join("\n")}
const config = { ${values.map((k) => `"${k}": _${k}`).join(", ")} }
export { config as default${values.length > 0 ? ", _" : ""}${values.join(", _")} }`,
          write: config.write
        });
        dtsContent.push(`declare module "${config.alias}/${subpath}" {${Object.keys(value).map((v) => ` export const _${v}: typeof import("${config.alias}/${join(`${key}/${subpath}`, `../${v}`)}")["default"];`).join("")} const defaultExport: { ${values.map((k) => `"${k}": typeof _${k}`).join(", ")} }; export default defaultExport; }`);
      }
    });
  };
  populateMap(resolvedConfig);
  const configOptions = Object.keys(resolvedConfig);
  const template = addTemplate({
    filename: "tailwind.config/index.mjs",
    getContents: () => `${configOptions.map((v) => `import ${v} from "#build/tailwind.config/${v}.mjs"`).join("\n")}
const config = { ${configOptions.join(", ")} }
export { config as default, ${configOptions.join(", ")} }`,
    write: true
  });
  dtsContent.push(`declare module "${config.alias}" {${configOptions.map((v) => ` export const ${v}: typeof import("${join(config.alias, v)}")["default"];`).join("")} const defaultExport: { ${configOptions.map((v) => `"${v}": typeof ${v}`)} }; export default defaultExport; }`);
  addTypeTemplate({
    filename: "types/tailwind.config.d.ts",
    getContents: () => dtsContent.join("\n")
  });
  nuxt.options.alias[config.alias] = dirname(template.dst);
}

function vitePlugin(tailwindConfig = {}, rootDir, cssPath) {
  const resolvedContent = (Array.isArray(tailwindConfig.content) ? tailwindConfig.content : tailwindConfig.content?.files || []).filter((f) => typeof f === "string").map((f) => !isAbsolute(f) ? resolve(rootDir, f) : f);
  return {
    name: "nuxt:tailwindcss",
    handleHotUpdate(ctx) {
      if (resolvedContent.findIndex((c) => micromatch.isMatch(ctx.file, c)) === -1) {
        return;
      }
      const extraModules = cssPath && ctx.server.moduleGraph.getModulesByFile(cssPath) || /* @__PURE__ */ new Set();
      const timestamp = +Date.now();
      for (const mod of extraModules) {
        ctx.server.moduleGraph.invalidateModule(mod, void 0, timestamp);
      }
      ctx.server.ws.send({
        type: "update",
        updates: Array.from(extraModules).map((mod) => {
          return {
            type: mod.type === "js" ? "js-update" : "css-update",
            path: mod.url,
            acceptedPath: mod.url,
            timestamp
          };
        })
      });
      if (ctx.file.includes("/content-cache/")) {
        return true;
      }
    }
  };
}

const setupViewer = async (twConfig, config, nuxt = useNuxt()) => {
  const route = joinURL(nuxt.options.app?.baseURL, config.endpoint);
  const [routeWithSlash, routeWithoutSlash] = [withTrailingSlash(route), withoutTrailingSlash(route)];
  const viewerServer = (await import('tailwind-config-viewer/server/index.js').then((r) => r.default || r))({ tailwindConfigProvider: () => twConfig }).asMiddleware();
  const viewerDevMiddleware = eventHandler((event) => viewerServer(event.node?.req || event.req, event.node?.res || event.res));
  if (isNuxt3()) {
    addDevServerHandler({
      handler: eventHandler((event) => {
        if (event.path === routeWithoutSlash) {
          return sendRedirect(event, routeWithSlash, 301);
        }
      })
    });
    addDevServerHandler({ route, handler: viewerDevMiddleware });
  }
  if (isNuxt2()) {
    nuxt.options.serverMiddleware.push(
      // @ts-expect-error untyped handler parameters
      (req, res, next) => {
        if (req.url === routeWithoutSlash) {
          return sendRedirect(new H3Event(req, res), routeWithSlash, 301);
        }
        next();
      },
      // @ts-expect-error untyped handler parameters
      { route, handler: (req, res) => viewerDevMiddleware(new H3Event(req, res)) }
    );
  }
  nuxt.hook("listen", (_, listener) => {
    const viewerUrl = cleanDoubleSlashes(joinURL(listener.url, config.endpoint));
    logger.info(`Tailwind Viewer: ${colors.underline(colors.yellow(withTrailingSlash(viewerUrl)))}`);
  });
};
const exportViewer = async (pathToConfig, config, nuxt = useNuxt()) => {
  if (!config.exportViewer) {
    return;
  }
  const cli = await import('tailwind-config-viewer/cli/export.js').then((r) => r.default || r);
  nuxt.hook("nitro:build:public-assets", (nitro) => {
    const dir = joinURL(nitro.options.output.publicDir, config.endpoint);
    cli(dir, pathToConfig);
    logger.success(`Exported viewer to ${colors.yellow(relative(nuxt.options.srcDir, dir))}`);
  });
};

const name = "@nuxtjs/tailwindcss";
const version = "6.11.4";
const configKey = "tailwindcss";
const compatibility = {
	nuxt: "^2.9.0 || ^3.0.0-rc.1"
};

const defaults = (nuxt = useNuxt()) => ({
  configPath: "tailwind.config",
  cssPath: join(nuxt.options.dir.assets, "css/tailwind.css"),
  config: defaultTailwindConfig,
  viewer: true,
  exposeConfig: false,
  disableHmrHotfix: false,
  quiet: nuxt.options.logLevel === "silent",
  editorSupport: false
});
const module = defineNuxtModule({
  meta: { name, version, configKey, compatibility },
  defaults,
  async setup(moduleOptions, nuxt) {
    if (moduleOptions.quiet)
      logger.level = LogLevels.silent;
    const deprecatedOptions = [
      ["addTwUtil", "Use `editorSupport.autocompleteUtil` instead."],
      ["exposeLevel", "Use `exposeConfig.level` instead."],
      ["injectPosition", `Use \`cssPath: [${moduleOptions.cssPath === join(nuxt.options.dir.assets, "css/tailwind.css") ? '"~/assets/css/tailwind.css"' : typeof moduleOptions.cssPath === "string" ? `"${moduleOptions.cssPath}"` : moduleOptions.cssPath}, { injectPosition: ${JSON.stringify(moduleOptions.injectPosition)} }]\` instead.`]
    ];
    deprecatedOptions.forEach(([dOption, alternative]) => moduleOptions[dOption] !== void 0 && logger.warn(`Deprecated \`${dOption}\`. ${alternative}`));
    const { resolve } = createResolver(import.meta.url);
    const [configPaths, contentPaths] = await resolveModulePaths(moduleOptions.configPath, nuxt);
    const tailwindConfig = await Promise.all(
      configPaths.map(async (configPath, idx, paths) => {
        let _tailwindConfig;
        try {
          _tailwindConfig = loadConfig(configPath);
        } catch (e) {
          logger.warn(`Failed to load Tailwind config at: \`./${relative(nuxt.options.rootDir, configPath)}\``, e);
        }
        if (_tailwindConfig && !_tailwindConfig.content) {
          _tailwindConfig.content = _tailwindConfig.purge;
        }
        await nuxt.callHook("tailwindcss:loadConfig", _tailwindConfig, configPath, idx, paths);
        return _tailwindConfig || {};
      })
    ).then((configs) => configs.reduce(
      (prev, curr) => configMerger(curr, prev),
      // internal default tailwind config
      configMerger(moduleOptions.config, { content: contentPaths })
    ));
    await nuxt.callHook("tailwindcss:config", tailwindConfig);
    const resolvedConfig = resolveConfig(tailwindConfig);
    await nuxt.callHook("tailwindcss:resolvedConfig", resolvedConfig);
    if (moduleOptions.exposeConfig) {
      const exposeConfig = resolveExposeConfig({ level: moduleOptions.exposeLevel, ...typeof moduleOptions.exposeConfig === "object" ? moduleOptions.exposeConfig : {} });
      createTemplates(resolvedConfig, exposeConfig, nuxt);
    }
    tailwindConfig._hash = String(Date.now());
    const [cssPath, cssPathConfig] = Array.isArray(moduleOptions.cssPath) ? moduleOptions.cssPath : [moduleOptions.cssPath];
    const [resolvedCss, loggerInfo] = await resolveCSSPath(
      typeof cssPath === "string" ? await resolvePath(cssPath, { extensions: [".css", ".sass", ".scss", ".less", ".styl"] }) : false,
      nuxt
    );
    logger.info(loggerInfo);
    nuxt.options.css = nuxt.options.css ?? [];
    const resolvedNuxtCss = resolvedCss && await Promise.all(nuxt.options.css.map((p) => resolvePath(p.src ?? p))) || [];
    if (resolvedCss && !resolvedNuxtCss.includes(resolvedCss)) {
      let injectPosition;
      try {
        injectPosition = resolveInjectPosition(nuxt.options.css, cssPathConfig?.injectPosition || moduleOptions.injectPosition);
      } catch (e) {
        throw new Error("failed to resolve Tailwind CSS injection position: " + e.message);
      }
      nuxt.options.css.splice(injectPosition, 0, resolvedCss);
    }
    const postcssOptions = nuxt.options.postcss || /* nuxt 3 */
    /* @ts-ignore */
    nuxt.options.build.postcss.postcssOptions || /* older nuxt3 */
    /* @ts-ignore */
    nuxt.options.build.postcss;
    postcssOptions.plugins = {
      ...postcssOptions.plugins || {},
      "tailwindcss/nesting": postcssOptions.plugins?.["tailwindcss/nesting"] ?? {},
      "postcss-custom-properties": postcssOptions.plugins?.["postcss-custom-properties"] ?? {},
      tailwindcss: tailwindConfig
    };
    if (parseFloat(getNuxtVersion()) < 2.16) {
      await installModule("@nuxt/postcss8").catch((e) => {
        logger.error(`Error occurred while loading \`@nuxt/postcss8\` required for Nuxt ${getNuxtVersion()}, is it installed?`);
        throw e;
      });
    }
    if (moduleOptions.editorSupport || moduleOptions.addTwUtil || isNuxt2()) {
      const editorSupportConfig = resolveEditorSupportConfig(moduleOptions.editorSupport);
      if ((editorSupportConfig.autocompleteUtil || moduleOptions.addTwUtil) && !isNuxt2()) {
        addImports({
          name: "autocompleteUtil",
          from: resolve("./runtime/utils"),
          as: "tw",
          ...typeof editorSupportConfig.autocompleteUtil === "object" ? editorSupportConfig.autocompleteUtil : {}
        });
      }
      if (editorSupportConfig.generateConfig || isNuxt2()) {
        addTemplate({
          filename: "tailwind.config.cjs",
          getContents: () => `module.exports = ${JSON.stringify(resolvedConfig, null, 2)}`,
          write: true,
          ...typeof editorSupportConfig.generateConfig === "object" ? editorSupportConfig.generateConfig : {}
        });
      }
    }
    if (nuxt.options.dev) {
      if (isNuxt2()) {
        nuxt.options.watch = nuxt.options.watch || [];
        configPaths.forEach((path) => nuxt.options.watch.push(path));
      } else if (Array.isArray(nuxt.options.watch)) {
        configPaths.forEach((path) => nuxt.options.watch.push(relative(nuxt.options.srcDir, path)));
      } else {
        const watcher = watch(configPaths, { depth: 0 }).on("change", (path) => {
          logger.info(`Tailwind config changed: ${path}`);
          logger.warn("Please restart the Nuxt server to apply changes or upgrade to latest Nuxt for automatic restart.");
        });
        nuxt.hook("close", () => watcher.close());
      }
      if (!moduleOptions.disableHmrHotfix) {
        addVitePlugin(vitePlugin(tailwindConfig, nuxt.options.rootDir, resolvedCss));
      }
      if (moduleOptions.viewer) {
        const viewerConfig = resolveViewerConfig(moduleOptions.viewer);
        setupViewer(tailwindConfig, viewerConfig, nuxt);
        nuxt.hook("devtools:customTabs", (tabs) => {
          tabs.push({
            title: "TailwindCSS",
            name: "tailwindcss",
            icon: "logos-tailwindcss-icon",
            category: "modules",
            view: {
              type: "iframe",
              src: withTrailingSlash(viewerConfig.endpoint)
            }
          });
        });
      }
    } else {
      if (moduleOptions.viewer) {
        const configTemplate = addTemplate({ filename: "tailwind.config/viewer-config.cjs", getContents: () => `module.exports = ${JSON.stringify(tailwindConfig)}`, write: true });
        exportViewer(configTemplate.dst, resolveViewerConfig(moduleOptions.viewer));
      }
    }
  }
});

export { module as default };
