#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/@nuxt+ui@3.1.3_@babel+parse_7a9c4136c496b19031bb5938edf59a72/node_modules/@nuxt/ui/cli/node_modules:/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/@nuxt+ui@3.1.3_@babel+parse_7a9c4136c496b19031bb5938edf59a72/node_modules/@nuxt/ui/node_modules:/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/@nuxt+ui@3.1.3_@babel+parse_7a9c4136c496b19031bb5938edf59a72/node_modules/@nuxt/node_modules:/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/@nuxt+ui@3.1.3_@babel+parse_7a9c4136c496b19031bb5938edf59a72/node_modules:/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/@nuxt+ui@3.1.3_@babel+parse_7a9c4136c496b19031bb5938edf59a72/node_modules/@nuxt/ui/cli/node_modules:/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/@nuxt+ui@3.1.3_@babel+parse_7a9c4136c496b19031bb5938edf59a72/node_modules/@nuxt/ui/node_modules:/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/@nuxt+ui@3.1.3_@babel+parse_7a9c4136c496b19031bb5938edf59a72/node_modules/@nuxt/node_modules:/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/@nuxt+ui@3.1.3_@babel+parse_7a9c4136c496b19031bb5938edf59a72/node_modules:/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@nuxt/ui/cli/index.mjs" "$@"
else
  exec node  "$basedir/../@nuxt/ui/cli/index.mjs" "$@"
fi
