#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/@nuxt+devtools@2.5.0_vite@6_09e26ee46aaf3bdb75e9dda861bed763/node_modules/@nuxt/devtools/node_modules:/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/@nuxt+devtools@2.5.0_vite@6_09e26ee46aaf3bdb75e9dda861bed763/node_modules/@nuxt/node_modules:/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/@nuxt+devtools@2.5.0_vite@6_09e26ee46aaf3bdb75e9dda861bed763/node_modules:/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/@nuxt+devtools@2.5.0_vite@6_09e26ee46aaf3bdb75e9dda861bed763/node_modules/@nuxt/devtools/node_modules:/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/@nuxt+devtools@2.5.0_vite@6_09e26ee46aaf3bdb75e9dda861bed763/node_modules/@nuxt/node_modules:/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/@nuxt+devtools@2.5.0_vite@6_09e26ee46aaf3bdb75e9dda861bed763/node_modules:/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../@nuxt/devtools/cli.mjs" "$@"
else
  exec node  "$basedir/../../../@nuxt/devtools/cli.mjs" "$@"
fi
