import { BaseComponent } from '../../component.js';
import type { ErrorMetadataProps } from '../../types.js';
/**
 * Displays the error metadata as cards
 */
export declare class ErrorMetadata extends BaseComponent<ErrorMetadataProps> {
    #private;
    /**
     * The toHTML method is used to output the HTML for the
     * web view
     */
    toHTML(props: ErrorMetadataProps): Promise<string>;
    /**
     * The toANSI method is used to output the text for the console
     */
    toANSI(): Promise<string>;
}
