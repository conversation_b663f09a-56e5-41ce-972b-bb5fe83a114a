{"name": "vite-dev-rpc", "type": "module", "version": "1.0.7", "description": "Remote procedure call for client-server communication in Vite plugins", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/vite-dev-rpc#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu/vite-dev-rpc.git"}, "bugs": {"url": "https://github.com/antfu/vite-dev-rpc/issues"}, "keywords": [], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/*", "./dist/index.d.ts"]}}, "files": ["dist"], "peerDependencies": {"vite": "^2.9.0 || ^3.0.0-0 || ^4.0.0-0 || ^5.0.0-0 || ^6.0.1"}, "dependencies": {"birpc": "^2.0.19", "vite-hot-client": "^2.0.4"}, "devDependencies": {"@antfu/eslint-config": "^3.12.2", "@antfu/ni": "^23.2.0", "@babel/types": "^7.26.3", "@types/node": "^22.10.5", "bumpp": "^9.9.3", "eslint": "^9.17.0", "esno": "^4.8.0", "pnpm": "^9.15.3", "rimraf": "^6.0.1", "typescript": "^5.7.3", "unbuild": "^3.2.0", "vite": "^6.0.7", "vitest": "^2.1.8"}, "scripts": {"build": "rimraf dist && unbuild", "dev": "unbuild --stub", "lint": "eslint .", "play": "nr dev && vite playground", "release": "bumpp && pnpm publish", "start": "esno src/index.ts", "test": "vitest", "typecheck": "tsc --noEmit"}}