<template>
  <div>
    <!-- <PERSON> Header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900">Site Settings</h1>
      <p class="mt-2 text-gray-600">Configure your store settings</p>
    </div>

    <form @submit.prevent="handleSubmit">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- General Settings -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">General Settings</h3>
          </template>

          <div class="space-y-4">
            <UFormGroup label="Site Name" required>
              <UInput
                v-model="settings.siteName"
                placeholder="Your Store Name"
                required
              />
            </UFormGroup>

            <UFormGroup label="Site Description">
              <UTextarea
                v-model="settings.siteDescription"
                placeholder="Brief description of your store"
                :rows="3"
              />
            </UFormGroup>

            <UFormGroup label="Logo URL">
              <UInput
                v-model="settings.logo"
                placeholder="https://example.com/logo.png"
              />
            </UFormGroup>

            <UFormGroup label="Contact Email">
              <UInput
                v-model="settings.contactEmail"
                type="email"
                placeholder="<EMAIL>"
              />
            </UFormGroup>

            <UFormGroup label="Contact Phone">
              <UInput
                v-model="settings.contactPhone"
                placeholder="+****************"
              />
            </UFormGroup>

            <UFormGroup label="Address">
              <UTextarea
                v-model="settings.address"
                placeholder="Your store address"
                :rows="2"
              />
            </UFormGroup>
          </div>
        </UCard>

        <!-- Notifications -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">Notifications</h3>
          </template>

          <div class="space-y-4">
            <UFormGroup>
              <UCheckbox
                v-model="settings.notifications.enabled"
                label="Enable notification bar"
              />
            </UFormGroup>

            <UFormGroup
              label="Notification Message"
              v-if="settings.notifications.enabled"
            >
              <UTextarea
                v-model="settings.notifications.message"
                placeholder="Your notification message"
                :rows="2"
              />
            </UFormGroup>
          </div>
        </UCard>

        <!-- GDPR Settings -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">GDPR & Privacy</h3>
          </template>

          <div class="space-y-4">
            <UFormGroup>
              <UCheckbox
                v-model="settings.gdpr.enabled"
                label="Enable GDPR cookie consent"
              />
            </UFormGroup>

            <UFormGroup label="GDPR Message" v-if="settings.gdpr.enabled">
              <UTextarea
                v-model="settings.gdpr.message"
                placeholder="Cookie consent message"
                :rows="2"
              />
            </UFormGroup>
          </div>
        </UCard>

        <!-- Email Settings -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">Email Settings</h3>
          </template>

          <div class="space-y-4">
            <UFormGroup>
              <UCheckbox
                v-model="settings.emailSubscription.enabled"
                label="Enable email subscription popup"
              />
            </UFormGroup>

            <UFormGroup
              label="Subscription Discount (%)"
              v-if="settings.emailSubscription.enabled"
            >
              <UInput
                v-model="settings.emailSubscription.discountPercent"
                type="number"
                placeholder="10"
              />
            </UFormGroup>

            <UFormGroup
              label="Popup Title"
              v-if="settings.emailSubscription.enabled"
            >
              <UInput
                v-model="settings.emailSubscription.popupTitle"
                placeholder="Get 10% Off Your First Order!"
              />
            </UFormGroup>

            <UFormGroup
              label="Popup Message"
              v-if="settings.emailSubscription.enabled"
            >
              <UTextarea
                v-model="settings.emailSubscription.popupMessage"
                placeholder="Subscribe to our newsletter and get exclusive offers"
                :rows="2"
              />
            </UFormGroup>
          </div>
        </UCard>

        <!-- Social Media -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">Social Media</h3>
          </template>

          <div class="space-y-4">
            <UFormGroup label="Facebook URL">
              <UInput
                v-model="settings.socialMedia.facebook"
                placeholder="https://facebook.com/yourstore"
              />
            </UFormGroup>

            <UFormGroup label="Instagram URL">
              <UInput
                v-model="settings.socialMedia.instagram"
                placeholder="https://instagram.com/yourstore"
              />
            </UFormGroup>

            <UFormGroup label="Twitter URL">
              <UInput
                v-model="settings.socialMedia.twitter"
                placeholder="https://twitter.com/yourstore"
              />
            </UFormGroup>

            <UFormGroup label="YouTube URL">
              <UInput
                v-model="settings.socialMedia.youtube"
                placeholder="https://youtube.com/yourstore"
              />
            </UFormGroup>
          </div>
        </UCard>

        <!-- SEO Settings -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">SEO Settings</h3>
          </template>

          <div class="space-y-4">
            <UFormGroup label="Meta Title">
              <UInput
                v-model="settings.seo.metaTitle"
                placeholder="Your Store - Best Products Online"
              />
            </UFormGroup>

            <UFormGroup label="Meta Description">
              <UTextarea
                v-model="settings.seo.metaDescription"
                placeholder="Shop the best products at great prices..."
                :rows="2"
              />
            </UFormGroup>

            <UFormGroup label="Meta Keywords">
              <UInput
                v-model="settings.seo.metaKeywords"
                placeholder="ecommerce, online store, products"
              />
            </UFormGroup>
          </div>
        </UCard>
      </div>

      <!-- Save Button -->
      <div class="mt-8 flex justify-end">
        <UButton type="submit" :loading="isLoading" size="lg">
          Save Settings
        </UButton>
      </div>
    </form>
  </div>
</template>

<script setup>
definePageMeta({
  layout: "admin-simple",
});

// Reactive data
const isLoading = ref(false);

const settings = ref({
  siteName: "",
  siteDescription: "",
  logo: "",
  contactEmail: "",
  contactPhone: "",
  address: "",
  notifications: {
    enabled: false,
    message: "",
  },
  gdpr: {
    enabled: false,
    message: "",
  },
  emailSubscription: {
    enabled: false,
    discountPercent: 10,
    popupTitle: "",
    popupMessage: "",
  },
  socialMedia: {
    facebook: "",
    instagram: "",
    twitter: "",
    youtube: "",
  },
  seo: {
    metaTitle: "",
    metaDescription: "",
    metaKeywords: "",
  },
});

// Fetch current settings
const fetchSettings = async () => {
  try {
    const { $api } = useNuxtApp();
    const response = await $api("/admin/settings");

    // Merge with default settings
    Object.assign(settings.value, response.settings);
  } catch (error) {
    console.error("Failed to fetch settings:", error);
  }
};

// Handle form submission
const handleSubmit = async () => {
  try {
    isLoading.value = true;
    const { $api } = useNuxtApp();

    await $api("/admin/settings", {
      method: "PUT",
      body: settings.value,
    });

    // Show success message
    console.log("Settings saved successfully");
  } catch (error) {
    console.error("Failed to save settings:", error);
  } finally {
    isLoading.value = false;
  }
};

// Load data on mount
onMounted(() => {
  fetchSettings();
});

// SEO
useHead({
  title: "Settings - Admin Panel",
});
</script>
