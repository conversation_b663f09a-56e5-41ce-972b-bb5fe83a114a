{"name": "supports-color", "version": "10.0.0", "description": "Detect whether a terminal supports color", "license": "MIT", "repository": "chalk/supports-color", "funding": "https://github.com/chalk/supports-color?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "node": "./index.js", "default": "./browser.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts", "browser.js", "browser.d.ts"], "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "devDependencies": {"@types/node": "^22.10.2", "ava": "^6.2.0", "tsd": "^0.31.2", "xo": "^0.60.0"}, "ava": {"serial": true, "workerThreads": false}}