<template>
  <div
    class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"
  >
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Create your account
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          Or
          <NuxtLink
            to="/login"
            class="font-medium text-blue-600 hover:text-blue-500"
          >
            sign in to your existing account
          </NuxtLink>
        </p>
      </div>

      <form class="mt-8 space-y-6" @submit.prevent="handleRegister">
        <div class="space-y-4">
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700"
              >Full Name</label
            >
            <UInput
              id="name"
              v-model="form.name"
              type="text"
              placeholder="Enter your full name"
              required
              :disabled="isLoading"
            />
          </div>

          <div>
            <label for="email" class="block text-sm font-medium text-gray-700"
              >Email Address</label
            >
            <UInput
              id="email"
              v-model="form.email"
              type="email"
              placeholder="Enter your email"
              required
              :disabled="isLoading"
            />
          </div>

          <div>
            <label
              for="password"
              class="block text-sm font-medium text-gray-700"
              >Password</label
            >
            <UInput
              id="password"
              v-model="form.password"
              type="password"
              placeholder="Create a password"
              required
              :disabled="isLoading"
            />
          </div>

          <div>
            <label
              for="confirmPassword"
              class="block text-sm font-medium text-gray-700"
              >Confirm Password</label
            >
            <UInput
              id="confirmPassword"
              v-model="form.confirmPassword"
              type="password"
              placeholder="Confirm your password"
              required
              :disabled="isLoading"
            />
          </div>
        </div>

        <div class="flex items-center">
          <input
            id="terms"
            v-model="form.acceptTerms"
            type="checkbox"
            :disabled="isLoading"
            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label for="terms" class="ml-2 block text-sm text-gray-900">
            I agree to the
            <NuxtLink to="/terms" class="text-blue-600 hover:text-blue-500"
              >Terms of Service</NuxtLink
            >
            and
            <NuxtLink to="/privacy" class="text-blue-600 hover:text-blue-500"
              >Privacy Policy</NuxtLink
            >
          </label>
        </div>

        <div>
          <UButton
            type="submit"
            :loading="isLoading"
            :disabled="!isFormValid"
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Create Account
          </UButton>
        </div>

        <div v-if="error" class="text-red-600 text-sm text-center">
          {{ error }}
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
const authStore = useAuthStore();
const router = useRouter();

// Form data
const form = ref({
  name: "",
  email: "",
  password: "",
  confirmPassword: "",
  acceptTerms: false,
});

const isLoading = ref(false);
const error = ref("");

// Computed
const isFormValid = computed(() => {
  return (
    form.value.name &&
    form.value.email &&
    form.value.password &&
    form.value.confirmPassword &&
    form.value.acceptTerms &&
    form.value.password === form.value.confirmPassword
  );
});

// Handle registration
const handleRegister = async () => {
  if (!isFormValid.value) {
    error.value = "Please fill in all fields and accept the terms";
    return;
  }

  if (form.value.password !== form.value.confirmPassword) {
    error.value = "Passwords do not match";
    return;
  }

  isLoading.value = true;
  error.value = "";

  try {
    await authStore.register({
      firstName: form.value.name,
      email: form.value.email,
      password: form.value.password,
    });

    // Redirect to home or intended page
    await router.push("/");
  } catch (err) {
    error.value = err.message || "Registration failed. Please try again.";
  } finally {
    isLoading.value = false;
  }
};

// SEO
useHead({
  title: "Register - Ecommerce Store",
  meta: [
    {
      name: "description",
      content:
        "Create a new account to start shopping and access exclusive features.",
    },
  ],
});
</script>
