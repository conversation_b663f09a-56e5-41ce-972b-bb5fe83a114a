#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/vite@6.3.5_@types+node@24.0_dd4f50dc288a5f596026332c6a892f16/node_modules/vite/bin/node_modules:/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/vite@6.3.5_@types+node@24.0_dd4f50dc288a5f596026332c6a892f16/node_modules/vite/node_modules:/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/vite@6.3.5_@types+node@24.0_dd4f50dc288a5f596026332c6a892f16/node_modules:/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/vite@6.3.5_@types+node@24.0_dd4f50dc288a5f596026332c6a892f16/node_modules/vite/bin/node_modules:/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/vite@6.3.5_@types+node@24.0_dd4f50dc288a5f596026332c6a892f16/node_modules/vite/node_modules:/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/vite@6.3.5_@types+node@24.0_dd4f50dc288a5f596026332c6a892f16/node_modules:/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../vite/bin/vite.js" "$@"
else
  exec node  "$basedir/../../../vite/bin/vite.js" "$@"
fi
