// ESLint config generated by Nuxt
/// <reference path="./eslint-typegen.d.ts" />
/* eslint-disable */
// @ts-nocheck

import typegen from '../node_modules/.pnpm/eslint-typegen@2.2.0_eslint@9.29.0_jiti@2.4.2_/node_modules/eslint-typegen/dist/index.mjs';
import { createConfigForNuxt, defineFlatConfigs, resolveOptions } from '../node_modules/.pnpm/@nuxt+eslint-config@1.4.1_@_b4b7b35b0d487f54973ab63cae8b6d53/node_modules/@nuxt/eslint-config/dist/flat.mjs';
import { fileURLToPath } from 'node:url';

const r = (...args) => fileURLToPath(new URL(...args, import.meta.url))

export { defineFlatConfigs }

export const options = resolveOptions({
  features: {
  "standalone": true
},
  dirs: {
    pages: ["pages"],
    composables: ["composables", "utils"],
    components: ["components"],
    componentsPrefixed: [],
    layouts: ["layouts"],
    plugins: ["plugins"],
    middleware: ["middleware"],
    modules: ["modules"],
    servers: [],
    root: [],
    src: [""],
}
})

export const configs = createConfigForNuxt(options)

configs.append(
// Set globals from imports registry
{
  name: 'nuxt/import-globals',
  languageOptions: {
    globals: Object.fromEntries(["asyncComputed","autoResetRef","computedAsync","computedEager","computedInject","computedWithControl","controlledComputed","controlledRef","createEventHook","createGlobalState","createInjectionState","createReactiveFn","createRef","createReusableTemplate","createSharedComposable","createTemplatePromise","createUnrefFn","debouncedRef","debouncedWatch","eagerComputed","extendRef","ignorableWatch","injectLocal","isDefined","makeDestructurable","onClickOutside","onElementRemoval","onKeyStroke","onLongPress","onStartTyping","pausableWatch","provideLocal","reactify","reactifyObject","reactiveComputed","reactiveOmit","reactivePick","refAutoReset","refDebounced","refDefault","refThrottled","refWithControl","resolveRef","resolveUnref","syncRef","syncRefs","templateRef","throttledRef","throttledWatch","toReactive","tryOnBeforeMount","tryOnBeforeUnmount","tryOnMounted","tryOnScopeDispose","tryOnUnmounted","unrefElement","until","useActiveElement","useAnimate","useArrayDifference","useArrayEvery","useArrayFilter","useArrayFind","useArrayFindIndex","useArrayFindLast","useArrayIncludes","useArrayJoin","useArrayMap","useArrayReduce","useArraySome","useArrayUnique","useAsyncQueue","useAsyncState","useBase64","useBattery","useBluetooth","useBreakpoints","useBroadcastChannel","useBrowserLocation","useCached","useClipboard","useClipboardItems","useCloned","useConfirmDialog","useCountdown","useCounter","useCssVar","useCurrentElement","useCycleList","useDark","useDateFormat","useDebounce","useDebouncedRefHistory","useDebounceFn","useDeviceMotion","useDeviceOrientation","useDevicePixelRatio","useDevicesList","useDisplayMedia","useDocumentVisibility","useDraggable","useDropZone","useElementBounding","useElementByPoint","useElementHover","useElementSize","useElementVisibility","useEventBus","useEventListener","useEventSource","useEyeDropper","useFavicon","useFileDialog","useFileSystemAccess","useFocus","useFocusWithin","useFps","useFullscreen","useGamepad","useGeolocation","useIdle","useInfiniteScroll","useIntersectionObserver","useInterval","useIntervalFn","useKeyModifier","useLastChanged","useLocalStorage","useMagicKeys","useManualRefHistory","useMediaControls","useMediaQuery","useMemoize","useMemory","useMounted","useMouse","useMouseInElement","useMousePressed","useMutationObserver","useNavigatorLanguage","useNetwork","useNow","useObjectUrl","useOffsetPagination","useOnline","usePageLeave","useParallax","useParentElement","usePerformanceObserver","usePermission","usePointer","usePointerLock","usePointerSwipe","usePreferredColorScheme","usePreferredContrast","usePreferredDark","usePreferredLanguages","usePreferredReducedMotion","usePreferredReducedTransparency","usePrevious","useRafFn","useRefHistory","useResizeObserver","useScreenOrientation","useScreenSafeArea","useScriptTag","useScroll","useScrollLock","useSessionStorage","useShare","useSorted","useSpeechRecognition","useSpeechSynthesis","useSSRWidth","useStepper","useStorageAsync","useStyleTag","useSupported","useSwipe","useTemplateRefsList","useTextareaAutosize","useTextDirection","useTextSelection","useThrottle","useThrottledRefHistory","useThrottleFn","useTimeAgo","useTimeout","useTimeoutFn","useTimeoutPoll","useTimestamp","useTitle","useToggle","useToNumber","useToString","useTransition","useUrlSearchParams","useUserMedia","useVibrate","useVirtualList","useVModel","useVModels","useWakeLock","useWebNotification","useWebSocket","useWebWorker","useWebWorkerFn","useWindowFocus","useWindowScroll","useWindowSize","watchArray","watchAtMost","watchDebounced","watchDeep","watchIgnorable","watchImmediate","watchOnce","watchPausable","watchThrottled","watchTriggerable","watchWithFilter","whenever","cancelIdleCallback","requestIdleCallback","setInterval","defineNuxtLink","clearNuxtData","refreshNuxtData","useAsyncData","useLazyAsyncData","useNuxtData","reloadNuxtApp","defineNuxtComponent","refreshCookie","useCookie","clearError","createError","isNuxtError","showError","useError","useFetch","useLazyFetch","injectHead","useHead","useHeadSafe","useSeoMeta","useServerHead","useServerHeadSafe","useServerSeoMeta","useHydration","useLoadingIndicator","getAppManifest","getRouteRules","callOnce","definePayloadReducer","definePayloadReviver","isPrerendered","loadPayload","preloadPayload","prefetchComponents","preloadComponents","preloadRouteComponents","usePreviewMode","onNuxtReady","useRouteAnnouncer","abortNavigation","addRouteMiddleware","defineNuxtRouteMiddleware","navigateTo","setPageLayout","useRoute","useRouter","useRuntimeHook","useScript","useScriptClarity","useScriptCloudflareWebAnalytics","useScriptCrisp","useScriptEventPage","useScriptFathomAnalytics","useScriptGoogleAdsense","useScriptGoogleAnalytics","useScriptGoogleMaps","useScriptGoogleTagManager","useScriptHotjar","useScriptIntercom","useScriptLemonSqueezy","useScriptMatomoAnalytics","useScriptMetaPixel","useScriptNpm","useScriptPlausibleAnalytics","useScriptRybbitAnalytics","useScriptSegment","useScriptSnapchatPixel","useScriptStripe","useScriptTriggerConsent","useScriptTriggerElement","useScriptUmamiAnalytics","useScriptVimeoPlayer","useScriptXPixel","useScriptYouTubePlayer","onPrehydrate","prerenderRoutes","setResponseStatus","useRequestEvent","useRequestFetch","useRequestHeader","useRequestHeaders","useResponseHeader","clearNuxtState","useState","useRequestURL","updateAppConfig","useAppConfig","defineAppConfig","defineNuxtPlugin","definePayloadPlugin","tryUseNuxtApp","useNuxtApp","useRuntimeConfig","useImage","defineLocale","defineShortcuts","extractShortcuts","avatarGroupInjectionKey","useAvatarGroup","buttonGroupInjectionKey","useButtonGroup","useComponentIcons","formBusInjectionKey","formFieldInjectionKey","formInputsInjectionKey","formLoadingInjectionKey","formOptionsInjectionKey","inputIdInjectionKey","useFormField","kbdKeysMap","useKbd","localeContextInjectionKey","useLocale","useOverlay","portalTargetInjectionKey","usePortal","useToast","useColorMode","defineI18nConfig","defineI18nLocale","defineI18nRoute","useBrowserLocale","useCookieLocale","useLocaleHead","useLocalePath","useLocaleRoute","useRouteBaseName","useSetI18nParams","useSwitchLocalePath","defineI18nConfig","defineI18nLocale","acceptHMRUpdate","defineStore","storeToRefs","usePinia","defineAppConfig","__buildAssetsURL","__publicAssetsURL","definePageMeta","useI18n","useAuthStore","useCartStore","useCategoriesStore","useProductsStore","useSiteStore","appendCorsHeaders","appendCorsPreflightHeaders","appendHeader","appendHeaders","appendResponseHeader","appendResponseHeaders","assertMethod","callNodeListener","clearResponseHeaders","clearSession","createApp","createAppEventHandler","createError","createEvent","createEventStream","createRouter","defaultContentType","defineEventHandler","defineLazyEventHandler","defineNodeListener","defineNodeMiddleware","defineRequestMiddleware","defineResponseMiddleware","defineWebSocket","defineWebSocketHandler","deleteCookie","dynamicEventHandler","eventHandler","fetchWithEvent","fromNodeMiddleware","fromPlainHandler","fromWebHandler","getCookie","getHeader","getHeaders","getMethod","getProxyRequestHeaders","getQuery","getRequestFingerprint","getRequestHeader","getRequestHeaders","getRequestHost","getRequestIP","getRequestPath","getRequestProtocol","getRequestURL","getRequestWebStream","getResponseHeader","getResponseHeaders","getResponseStatus","getResponseStatusText","getRouterParam","getRouterParams","getSession","getValidatedQuery","getValidatedRouterParams","handleCacheHeaders","handleCors","isCorsOriginAllowed","isError","isEvent","isEventHandler","isMethod","isPreflightRequest","isStream","isWebResponse","lazyEventHandler","parseCookies","promisifyNodeListener","proxyRequest","readBody","readFormData","readMultipartFormData","readRawBody","readValidatedBody","removeResponseHeader","sanitizeStatusCode","sanitizeStatusMessage","sealSession","send","sendError","sendIterable","sendNoContent","sendProxy","sendRedirect","sendStream","sendWebResponse","serveStatic","setCookie","setHeader","setHeaders","setResponseHeader","setResponseHeaders","setResponseStatus","splitCookiesString","toEventHandler","toNodeListener","toPlainHandler","toWebHandler","toWebRequest","unsealSession","updateSession","useBase","useSession","writeEarlyHints","useNitroApp","cachedEventHandler","cachedFunction","defineCachedEventHandler","defineCachedFunction","useAppConfig","useRuntimeConfig","useEvent","defineNitroErrorHandler","defineRouteMeta","defineNitroPlugin","nitroPlugin","defineRenderHandler","getRouteRules","useStorage","defineTask","runTask","Component","ComponentPublicInstance","computed","ComputedRef","customRef","defineAsyncComponent","defineComponent","DirectiveBinding","effect","effectScope","ExtractDefaultPropTypes","ExtractPropTypes","ExtractPublicPropTypes","getCurrentInstance","getCurrentScope","h","hasInjectionContext","inject","InjectionKey","isProxy","isReactive","isReadonly","isRef","isShallow","markRaw","MaybeRef","MaybeRefOrGetter","mergeModels","nextTick","onActivated","onBeforeMount","onBeforeUnmount","onBeforeUpdate","onDeactivated","onErrorCaptured","onMounted","onRenderTracked","onRenderTriggered","onScopeDispose","onServerPrefetch","onUnmounted","onUpdated","PropType","provide","proxyRefs","reactive","readonly","ref","Ref","resolveComponent","shallowReactive","shallowReadonly","shallowRef","toRaw","toRef","toRefs","toValue","triggerRef","unref","useAttrs","useCssModule","useCssVars","useId","useModel","useShadowRoot","useSlots","useTemplateRef","useTransitionState","VNode","watch","watchEffect","watchPostEffect","watchSyncEffect","withCtx","withDirectives","withKeys","withMemo","withModifiers","withScopeId","WritableComputedRef","isVue2","isVue3","onBeforeRouteLeave","onBeforeRouteUpdate","useLink"].map(i => [i, 'readonly'])),
  },
}
)

export function withNuxt(...customs) {
  return configs
    .clone()
    .append(...customs)
    .onResolved(configs => typegen(configs, { dtsPath: r("./eslint-typegen.d.ts"), augmentFlatConfigUtils: true }))
}

export default withNuxt