#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="C:\Users\<USER>\OneDrive\Töölaud\ecommerce\client\node_modules\.pnpm\nuxt@3.17.5_@parcel+watcher_3bcad18f22b406d986ef10e197137938\node_modules\nuxt\bin\node_modules;C:\Users\<USER>\OneDrive\Töölaud\ecommerce\client\node_modules\.pnpm\nuxt@3.17.5_@parcel+watcher_3bcad18f22b406d986ef10e197137938\node_modules\nuxt\node_modules;C:\Users\<USER>\OneDrive\Töölaud\ecommerce\client\node_modules\.pnpm\nuxt@3.17.5_@parcel+watcher_3bcad18f22b406d986ef10e197137938\node_modules;C:\Users\<USER>\OneDrive\Töölaud\ecommerce\client\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/nuxt@3.17.5_@parcel+watcher_3bcad18f22b406d986ef10e197137938/node_modules/nuxt/bin/node_modules:/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/nuxt@3.17.5_@parcel+watcher_3bcad18f22b406d986ef10e197137938/node_modules/nuxt/node_modules:/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/nuxt@3.17.5_@parcel+watcher_3bcad18f22b406d986ef10e197137938/node_modules:/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../nuxt/bin/nuxt.mjs" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../nuxt/bin/nuxt.mjs" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../nuxt/bin/nuxt.mjs" $args
  } else {
    & "node$exe"  "$basedir/../nuxt/bin/nuxt.mjs" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
