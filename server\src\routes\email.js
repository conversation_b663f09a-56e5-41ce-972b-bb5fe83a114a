const express = require('express');
const { body, validationResult } = require('express-validator');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const nodemailer = require('nodemailer');

const router = express.Router();
const prisma = new PrismaClient();

// Create nodemailer transporter
const transporter = nodemailer.createTransporter({
  host: process.env.EMAIL_HOST,
  port: process.env.EMAIL_PORT,
  secure: process.env.EMAIL_SECURE === 'true',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS
  }
});

// Subscribe to newsletter
router.post('/subscribe', [
  body('email').isEmail().normalizeEmail(),
  body('source').optional().trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { email, source = 'popup' } = req.body;

    // Check if already subscribed
    const existing = await prisma.emailSubscriber.findUnique({
      where: { email }
    });

    if (existing) {
      if (existing.isActive) {
        return res.status(400).json({ error: 'Email already subscribed' });
      } else {
        // Reactivate subscription
        await prisma.emailSubscriber.update({
          where: { email },
          data: { isActive: true, source }
        });
      }
    } else {
      // Create new subscription
      await prisma.emailSubscriber.create({
        data: { email, source }
      });
    }

    // Send welcome email with discount code
    try {
      await transporter.sendMail({
        from: process.env.EMAIL_USER,
        to: email,
        subject: 'Welcome! Here\'s your 15% discount',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2>Welcome to our newsletter!</h2>
            <p>Thank you for subscribing. Here's your exclusive 15% discount code:</p>
            <div style="background: #f0f0f0; padding: 20px; text-align: center; margin: 20px 0;">
              <h3 style="color: #333; font-size: 24px; margin: 0;">WELCOME15</h3>
            </div>
            <p>Use this code at checkout to get 15% off your first order.</p>
            <p>Happy shopping!</p>
          </div>
        `
      });
    } catch (emailError) {
      console.error('Welcome email error:', emailError);
      // Don't fail the subscription if email fails
    }

    res.json({ 
      message: 'Successfully subscribed to newsletter',
      discountCode: 'WELCOME15'
    });
  } catch (error) {
    console.error('Subscribe error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Unsubscribe from newsletter
router.post('/unsubscribe', [
  body('email').isEmail().normalizeEmail()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { email } = req.body;

    await prisma.emailSubscriber.updateMany({
      where: { email },
      data: { isActive: false }
    });

    res.json({ message: 'Successfully unsubscribed' });
  } catch (error) {
    console.error('Unsubscribe error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Admin: Get all subscribers
router.get('/subscribers', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { page = 1, limit = 50, active = 'true' } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    const where = active === 'all' ? {} : { isActive: active === 'true' };

    const [subscribers, total] = await Promise.all([
      prisma.emailSubscriber.findMany({
        where,
        skip,
        take,
        orderBy: { createdAt: 'desc' }
      }),
      prisma.emailSubscriber.count({ where })
    ]);

    const totalPages = Math.ceil(total / take);

    res.json({
      subscribers,
      pagination: {
        page: parseInt(page),
        limit: take,
        total,
        totalPages
      }
    });
  } catch (error) {
    console.error('Get subscribers error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Admin: Send email to subscribers
router.post('/send', authenticateToken, requireAdmin, [
  body('subject').notEmpty().trim(),
  body('content').notEmpty(),
  body('recipients').isArray().notEmpty(),
  body('sendToAll').optional().isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { subject, content, recipients, sendToAll = false } = req.body;

    let emailList;

    if (sendToAll) {
      const subscribers = await prisma.emailSubscriber.findMany({
        where: { isActive: true },
        select: { email: true }
      });
      emailList = subscribers.map(s => s.email);
    } else {
      emailList = recipients;
    }

    if (emailList.length === 0) {
      return res.status(400).json({ error: 'No recipients found' });
    }

    // Send emails in batches to avoid overwhelming the SMTP server
    const batchSize = 10;
    const batches = [];
    for (let i = 0; i < emailList.length; i += batchSize) {
      batches.push(emailList.slice(i, i + batchSize));
    }

    let successCount = 0;
    let failureCount = 0;

    for (const batch of batches) {
      const promises = batch.map(async (email) => {
        try {
          await transporter.sendMail({
            from: process.env.EMAIL_USER,
            to: email,
            subject,
            html: content
          });
          successCount++;
        } catch (error) {
          console.error(`Failed to send email to ${email}:`, error);
          failureCount++;
        }
      });

      await Promise.all(promises);
      
      // Small delay between batches
      if (batches.indexOf(batch) < batches.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    res.json({
      message: 'Email campaign completed',
      successCount,
      failureCount,
      totalRecipients: emailList.length
    });
  } catch (error) {
    console.error('Send email error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
