<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50">
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Test Login Page
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          This is a simplified login page for testing
        </p>
      </div>

      <form class="mt-8 space-y-6" @submit.prevent="handleLogin">
        <div class="space-y-4">
          <div>
            <label
              for="email"
              class="block text-sm font-medium text-gray-700 mb-2"
            >
              Email address
            </label>
            <input
              id="email"
              v-model="form.email"
              type="email"
              placeholder="Enter your email address"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label
              for="password"
              class="block text-sm font-medium text-gray-700 mb-2"
            >
              Password
            </label>
            <input
              id="password"
              v-model="form.password"
              type="password"
              placeholder="Enter your password"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        <div>
          <button
            type="submit"
            :disabled="isLoading || !form.email || !form.password"
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {{ isLoading ? "Signing in..." : "Sign in" }}
          </button>
        </div>

        <div v-if="error" class="text-red-600 text-sm text-center">
          {{ error }}
        </div>

        <div v-if="success" class="text-green-600 text-sm text-center">
          {{ success }}
        </div>
      </form>

      <div class="text-center">
        <NuxtLink to="/" class="text-blue-600 hover:text-blue-500">
          Back to Home
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<script setup>
const authStore = useAuthStore();
const router = useRouter();

// Form data
const form = ref({
  email: "<EMAIL>", // Pre-filled for testing
  password: "admin123",
});

const isLoading = ref(false);
const error = ref("");
const success = ref("");

// Handle login
const handleLogin = async () => {
  if (!form.value.email || !form.value.password) {
    error.value = "Please fill in all fields";
    return;
  }

  isLoading.value = true;
  error.value = "";
  success.value = "";

  try {
    await authStore.login(form.value.email, form.value.password);
    success.value = "Login successful! Redirecting...";

    // Redirect after a short delay
    setTimeout(() => {
      if (authStore.isAdmin) {
        router.push("/admin");
      } else {
        router.push("/");
      }
    }, 1000);
  } catch (err) {
    error.value = err.message || "Login failed. Please try again.";
  } finally {
    isLoading.value = false;
  }
};

// Use simple layout to avoid component issues
definePageMeta({
  layout: "default-simple",
});

// SEO
useHead({
  title: "Test Login - Ecommerce Store",
});
</script>
