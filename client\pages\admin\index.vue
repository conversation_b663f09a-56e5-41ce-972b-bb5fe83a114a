<template>
  <div>
    <!-- <PERSON> Header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900">Dashboard</h1>
      <p class="mt-2 text-gray-600">
        Welcome back! Here's what's happening with your store.
      </p>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <UIcon
              name="i-heroicons-shopping-bag"
              class="h-8 w-8 text-blue-500"
            />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Total Orders</p>
            <p class="text-2xl font-bold text-gray-900">
              {{ stats.totalOrders }}
            </p>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <UIcon
              name="i-heroicons-currency-dollar"
              class="h-8 w-8 text-green-500"
            />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Total Revenue</p>
            <p class="text-2xl font-bold text-gray-900">
              ${{ stats.totalRevenue }}
            </p>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <UIcon name="i-heroicons-cube" class="h-8 w-8 text-purple-500" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Total Products</p>
            <p class="text-2xl font-bold text-gray-900">
              {{ stats.totalProducts }}
            </p>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <UIcon name="i-heroicons-users" class="h-8 w-8 text-orange-500" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Total Users</p>
            <p class="text-2xl font-bold text-gray-900">
              {{ stats.totalUsers }}
            </p>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Recent Orders and Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Recent Orders -->
      <UCard>
        <template #header>
          <div class="flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-900">Recent Orders</h3>
            <UButton to="/admin/orders" variant="ghost" size="sm"
              >View All</UButton
            >
          </div>
        </template>

        <div class="space-y-4">
          <div
            v-if="recentOrders.length === 0"
            class="text-center py-8 text-gray-500"
          >
            No orders yet
          </div>
          <div
            v-for="order in recentOrders"
            :key="order.id"
            class="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
          >
            <div>
              <p class="font-medium text-gray-900">#{{ order.orderNumber }}</p>
              <p class="text-sm text-gray-500">
                {{ order.user?.firstName }} {{ order.user?.lastName }}
              </p>
            </div>
            <div class="text-right">
              <p class="font-medium text-gray-900">${{ order.total }}</p>
              <UBadge
                :color="getOrderStatusColor(order.status)"
                variant="soft"
                size="sm"
              >
                {{ order.status }}
              </UBadge>
            </div>
          </div>
        </div>
      </UCard>

      <!-- Quick Actions -->
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold text-gray-900">Quick Actions</h3>
        </template>

        <div class="grid grid-cols-2 gap-4">
          <UButton
            to="/admin/products/new"
            icon="i-heroicons-plus"
            block
            class="justify-center"
          >
            Add Product
          </UButton>

          <UButton
            to="/admin/categories/new"
            icon="i-heroicons-plus"
            variant="outline"
            block
            class="justify-center"
          >
            Add Category
          </UButton>

          <UButton
            to="/admin/slideshows/new"
            icon="i-heroicons-plus"
            variant="outline"
            block
            class="justify-center"
          >
            Add Slideshow
          </UButton>

          <UButton
            to="/admin/settings"
            icon="i-heroicons-cog-6-tooth"
            variant="outline"
            block
            class="justify-center"
          >
            Site Settings
          </UButton>
        </div>
      </UCard>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  layout: "admin",
  middleware: "admin",
});

// Reactive data
const stats = ref({
  totalOrders: 0,
  totalRevenue: 0,
  totalProducts: 0,
  totalUsers: 0,
});

const recentOrders = ref([]);
const isLoading = ref(true);

// Helper function for order status colors
const getOrderStatusColor = (status) => {
  const colors = {
    PENDING: "yellow",
    CONFIRMED: "blue",
    PROCESSING: "purple",
    SHIPPED: "orange",
    DELIVERED: "green",
    CANCELLED: "red",
    REFUNDED: "gray",
  };
  return colors[status] || "gray";
};

// Fetch dashboard data
const fetchDashboardData = async () => {
  try {
    const { $api } = useNuxtApp();

    // Fetch stats
    const statsResponse = await $api("/admin/dashboard/stats");
    stats.value = statsResponse;

    // Fetch recent orders
    const ordersResponse = await $api("/admin/orders?limit=5");
    recentOrders.value = ordersResponse.orders || [];
  } catch (error) {
    console.error("Failed to fetch dashboard data:", error);
  } finally {
    isLoading.value = false;
  }
};

// Load data on mount
onMounted(() => {
  fetchDashboardData();
});

// SEO
useHead({
  title: "Dashboard - Admin Panel",
});
</script>
