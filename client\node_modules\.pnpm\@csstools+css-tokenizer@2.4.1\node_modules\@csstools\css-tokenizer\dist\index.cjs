"use strict";class ParseError extends Error{sourceStart;sourceEnd;parserState;constructor(e,o,n,r){super(e),this.name="ParseError",this.sourceStart=o,this.sourceEnd=n,this.parserState=r}}class ParseErrorWithToken extends ParseError{token;constructor(e,o,n,r,t){super(e,o,n,r),this.token=t}}const e={UnexpectedNewLineInString:"Unexpected newline while consuming a string token.",UnexpectedEOFInString:"Unexpected EOF while consuming a string token.",UnexpectedEOFInComment:"Unexpected EOF while consuming a comment.",UnexpectedEOFInURL:"Unexpected EOF while consuming a url token.",UnexpectedEOFInEscapedCodePoint:"Unexpected EOF while consuming an escaped code point.",UnexpectedCharacterInURL:"Unexpected character while consuming a url token.",InvalidEscapeSequenceInURL:"Invalid escape sequence while consuming a url token.",InvalidEscapeSequenceAfterBackslash:'Invalid escape sequence after "\\"'};class Reader{cursor=0;source="";codePointSource=[];representationIndices=[-1];length=0;representationStart=0;representationEnd=-1;constructor(e){this.source=e;{let o=-1,n="";for(n of e)o+=n.length,this.codePointSource.push(n.codePointAt(0)),this.representationIndices.push(o)}this.length=this.codePointSource.length}advanceCodePoint(e=1){this.cursor=this.cursor+e,this.representationEnd=this.representationIndices[this.cursor]}readCodePoint(e=1){const o=this.codePointSource[this.cursor];return void 0!==o&&(this.cursor=this.cursor+e,this.representationEnd=this.representationIndices[this.cursor],o)}unreadCodePoint(e=1){this.cursor=this.cursor-e,this.representationEnd=this.representationIndices[this.cursor]}resetRepresentation(){this.representationStart=this.representationIndices[this.cursor]+1,this.representationEnd=-1}}const o="undefined"!=typeof globalThis&&"structuredClone"in globalThis;const n=39,r=42,t=8,i=13,s=9,c=58,a=44,u=64,d=127,p=33,T=12,P=46,S=62,k=45,C=31,l=69,f=101,x=123,m=40,h=91,E=60,y=10,v=11,g=95,I=1114111,U=0,O=35,D=37,R=43,w=34,L=65533,A=92,W=125,N=41,F=93,q=59,b=14,H=47,B=32,V=117,z=85,K=114,M=82,$=108,J=76,_=63,j=48,Q=70;function checkIfFourCodePointsWouldStartCDO(e){return e.codePointSource[e.cursor]===E&&e.codePointSource[e.cursor+1]===p&&e.codePointSource[e.cursor+2]===k&&e.codePointSource[e.cursor+3]===k}function isDigitCodePoint(e){return e>=48&&e<=57}function isUppercaseLetterCodePoint(e){return e>=65&&e<=90}function isLowercaseLetterCodePoint(e){return e>=97&&e<=122}function isHexDigitCodePoint(e){return isDigitCodePoint(e)||e>=97&&e<=102||e>=65&&e<=70}function isLetterCodePoint(e){return isLowercaseLetterCodePoint(e)||isUppercaseLetterCodePoint(e)}function isIdentStartCodePoint(e){return isLetterCodePoint(e)||isNonASCII_IdentCodePoint(e)||e===g}function isIdentCodePoint(e){return isIdentStartCodePoint(e)||isDigitCodePoint(e)||e===k}function isNonASCII_IdentCodePoint(e){return 183===e||8204===e||8205===e||8255===e||8256===e||8204===e||(192<=e&&e<=214||216<=e&&e<=246||248<=e&&e<=893||895<=e&&e<=8191||8304<=e&&e<=8591||11264<=e&&e<=12271||12289<=e&&e<=55295||63744<=e&&e<=64975||65008<=e&&e<=65533||e>=65536)}function isNewLine(e){return e===y||e===i||e===T}function isWhitespace(e){return e===B||e===y||e===s||e===i||e===T}function checkIfTwoCodePointsAreAValidEscape(e){return e.codePointSource[e.cursor]===A&&!isNewLine(e.codePointSource[e.cursor+1])}function checkIfThreeCodePointsWouldStartAnIdentSequence(e,o){return o.codePointSource[o.cursor]===k?o.codePointSource[o.cursor+1]===k||(!!isIdentStartCodePoint(o.codePointSource[o.cursor+1])||o.codePointSource[o.cursor+1]===A&&!isNewLine(o.codePointSource[o.cursor+2])):!!isIdentStartCodePoint(o.codePointSource[o.cursor])||checkIfTwoCodePointsAreAValidEscape(o)}function checkIfThreeCodePointsWouldStartANumber(e){return e.codePointSource[e.cursor]===R||e.codePointSource[e.cursor]===k?!!isDigitCodePoint(e.codePointSource[e.cursor+1])||e.codePointSource[e.cursor+1]===P&&isDigitCodePoint(e.codePointSource[e.cursor+2]):e.codePointSource[e.cursor]===P?isDigitCodePoint(e.codePointSource[e.cursor+1]):isDigitCodePoint(e.codePointSource[e.cursor])}function checkIfTwoCodePointsStartAComment(e){return e.codePointSource[e.cursor]===H&&e.codePointSource[e.cursor+1]===r}function checkIfThreeCodePointsWouldStartCDC(e){return e.codePointSource[e.cursor]===k&&e.codePointSource[e.cursor+1]===k&&e.codePointSource[e.cursor+2]===S}var G,X,Y;function consumeComment(o,n){for(n.advanceCodePoint(2);;){const t=n.readCodePoint();if(!1===t){const r=[exports.TokenType.Comment,n.source.slice(n.representationStart,n.representationEnd+1),n.representationStart,n.representationEnd,void 0];return o.onParseError(new ParseErrorWithToken(e.UnexpectedEOFInComment,n.representationStart,n.representationEnd,["4.3.2. Consume comments","Unexpected EOF"],r)),r}if(t===r&&(void 0!==n.codePointSource[n.cursor]&&n.codePointSource[n.cursor]===H)){n.advanceCodePoint();break}}return[exports.TokenType.Comment,n.source.slice(n.representationStart,n.representationEnd+1),n.representationStart,n.representationEnd,void 0]}function consumeEscapedCodePoint(o,n){const r=n.readCodePoint();if(!1===r)return o.onParseError(new ParseError(e.UnexpectedEOFInEscapedCodePoint,n.representationStart,n.representationEnd,["4.3.7. Consume an escaped code point","Unexpected EOF"])),L;if(isHexDigitCodePoint(r)){const e=[r];for(;void 0!==n.codePointSource[n.cursor]&&isHexDigitCodePoint(n.codePointSource[n.cursor])&&e.length<6;)e.push(n.codePointSource[n.cursor]),n.advanceCodePoint();isWhitespace(n.codePointSource[n.cursor])&&n.advanceCodePoint();const o=parseInt(String.fromCodePoint(...e),16);return 0===o?L:(t=o)>=55296&&t<=57343||o>I?L:o}var t;return r}function consumeIdentSequence(e,o){const n=[];for(;;)if(isIdentCodePoint(o.codePointSource[o.cursor]))n.push(o.codePointSource[o.cursor]),o.advanceCodePoint();else{if(!checkIfTwoCodePointsAreAValidEscape(o))return n;o.advanceCodePoint(),n.push(consumeEscapedCodePoint(e,o))}}function consumeHashToken(e,o){if(o.advanceCodePoint(),void 0!==o.codePointSource[o.cursor]&&(isIdentCodePoint(o.codePointSource[o.cursor])||checkIfTwoCodePointsAreAValidEscape(o))){let n=exports.HashType.Unrestricted;checkIfThreeCodePointsWouldStartAnIdentSequence(0,o)&&(n=exports.HashType.ID);const r=consumeIdentSequence(e,o);return[exports.TokenType.Hash,o.source.slice(o.representationStart,o.representationEnd+1),o.representationStart,o.representationEnd,{value:String.fromCodePoint(...r),type:n}]}return[exports.TokenType.Delim,"#",o.representationStart,o.representationEnd,{value:"#"}]}function consumeNumber(e,o){let n=exports.NumberType.Integer;for(o.codePointSource[o.cursor]!==R&&o.codePointSource[o.cursor]!==k||o.advanceCodePoint();isDigitCodePoint(o.codePointSource[o.cursor]);)o.advanceCodePoint();if(o.codePointSource[o.cursor]===P&&isDigitCodePoint(o.codePointSource[o.cursor+1]))for(o.advanceCodePoint(2),n=exports.NumberType.Number;isDigitCodePoint(o.codePointSource[o.cursor]);)o.advanceCodePoint();if(o.codePointSource[o.cursor]===f||o.codePointSource[o.cursor]===l){if(isDigitCodePoint(o.codePointSource[o.cursor+1]))o.advanceCodePoint(2);else{if(o.codePointSource[o.cursor+1]!==k&&o.codePointSource[o.cursor+1]!==R||!isDigitCodePoint(o.codePointSource[o.cursor+2]))return n;o.advanceCodePoint(3)}for(n=exports.NumberType.Number;isDigitCodePoint(o.codePointSource[o.cursor]);)o.advanceCodePoint()}return n}function consumeNumericToken(e,o){let n;{const e=o.codePointSource[o.cursor];e===k?n="-":e===R&&(n="+")}const r=consumeNumber(0,o),t=parseFloat(o.source.slice(o.representationStart,o.representationEnd+1));if(checkIfThreeCodePointsWouldStartAnIdentSequence(0,o)){const i=consumeIdentSequence(e,o);return[exports.TokenType.Dimension,o.source.slice(o.representationStart,o.representationEnd+1),o.representationStart,o.representationEnd,{value:t,signCharacter:n,type:r,unit:String.fromCodePoint(...i)}]}return o.codePointSource[o.cursor]===D?(o.advanceCodePoint(),[exports.TokenType.Percentage,o.source.slice(o.representationStart,o.representationEnd+1),o.representationStart,o.representationEnd,{value:t,signCharacter:n}]):[exports.TokenType.Number,o.source.slice(o.representationStart,o.representationEnd+1),o.representationStart,o.representationEnd,{value:t,signCharacter:n,type:r}]}function consumeWhiteSpace(e){for(;isWhitespace(e.codePointSource[e.cursor]);)e.advanceCodePoint();return[exports.TokenType.Whitespace,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,void 0]}function consumeStringToken(o,n){let r="";const t=n.readCodePoint();for(;;){const s=n.readCodePoint();if(!1===s){const t=[exports.TokenType.String,n.source.slice(n.representationStart,n.representationEnd+1),n.representationStart,n.representationEnd,{value:r}];return o.onParseError(new ParseErrorWithToken(e.UnexpectedEOFInString,n.representationStart,n.representationEnd,["4.3.5. Consume a string token","Unexpected EOF"],t)),t}if(isNewLine(s)){n.unreadCodePoint();const r=[exports.TokenType.BadString,n.source.slice(n.representationStart,n.representationEnd+1),n.representationStart,n.representationEnd,void 0];return o.onParseError(new ParseErrorWithToken(e.UnexpectedNewLineInString,n.representationStart,n.codePointSource[n.cursor]===i&&n.codePointSource[n.cursor+1]===y?n.representationEnd+2:n.representationEnd+1,["4.3.5. Consume a string token","Unexpected newline"],r)),r}if(s===t)return[exports.TokenType.String,n.source.slice(n.representationStart,n.representationEnd+1),n.representationStart,n.representationEnd,{value:r}];if(s!==A)r+=String.fromCodePoint(s);else{if(void 0===n.codePointSource[n.cursor])continue;if(isNewLine(n.codePointSource[n.cursor])){n.codePointSource[n.cursor]===i&&n.codePointSource[n.cursor+1]===y&&n.advanceCodePoint(),n.advanceCodePoint();continue}r+=String.fromCodePoint(consumeEscapedCodePoint(o,n))}}}function checkIfCodePointsMatchURLIdent(e){return!(3!==e.length||e[0]!==V&&e[0]!==z||e[1]!==K&&e[1]!==M||e[2]!==$&&e[2]!==J)}function consumeBadURL(e,o){for(;;){if(void 0===o.codePointSource[o.cursor])return;if(o.codePointSource[o.cursor]===N)return void o.advanceCodePoint();checkIfTwoCodePointsAreAValidEscape(o)?(o.advanceCodePoint(),consumeEscapedCodePoint(e,o)):o.advanceCodePoint()}}function consumeUrlToken(o,r){for(;isWhitespace(r.codePointSource[r.cursor]);)r.advanceCodePoint();let i="";for(;;){if(void 0===r.codePointSource[r.cursor]){const n=[exports.TokenType.URL,r.source.slice(r.representationStart,r.representationEnd+1),r.representationStart,r.representationEnd,{value:i}];return o.onParseError(new ParseErrorWithToken(e.UnexpectedEOFInURL,r.representationStart,r.representationEnd,["4.3.6. Consume a url token","Unexpected EOF"],n)),n}if(r.codePointSource[r.cursor]===N)return r.advanceCodePoint(),[exports.TokenType.URL,r.source.slice(r.representationStart,r.representationEnd+1),r.representationStart,r.representationEnd,{value:i}];if(isWhitespace(r.codePointSource[r.cursor])){for(r.advanceCodePoint();isWhitespace(r.codePointSource[r.cursor]);)r.advanceCodePoint();if(void 0===r.codePointSource[r.cursor]){const n=[exports.TokenType.URL,r.source.slice(r.representationStart,r.representationEnd+1),r.representationStart,r.representationEnd,{value:i}];return o.onParseError(new ParseErrorWithToken(e.UnexpectedEOFInURL,r.representationStart,r.representationEnd,["4.3.6. Consume a url token","Consume as much whitespace as possible","Unexpected EOF"],n)),n}return r.codePointSource[r.cursor]===N?(r.advanceCodePoint(),[exports.TokenType.URL,r.source.slice(r.representationStart,r.representationEnd+1),r.representationStart,r.representationEnd,{value:i}]):(consumeBadURL(o,r),[exports.TokenType.BadURL,r.source.slice(r.representationStart,r.representationEnd+1),r.representationStart,r.representationEnd,void 0])}if(r.codePointSource[r.cursor]===w||r.codePointSource[r.cursor]===n||r.codePointSource[r.cursor]===m||((s=r.codePointSource[r.cursor])===v||s===d||U<=s&&s<=t||b<=s&&s<=C)){consumeBadURL(o,r);const n=[exports.TokenType.BadURL,r.source.slice(r.representationStart,r.representationEnd+1),r.representationStart,r.representationEnd,void 0];return o.onParseError(new ParseErrorWithToken(e.UnexpectedCharacterInURL,r.representationStart,r.representationEnd,["4.3.6. Consume a url token","Unexpected U+0022 QUOTATION MARK (\"), U+0027 APOSTROPHE ('), U+0028 LEFT PARENTHESIS (() or non-printable code point"],n)),n}if(r.codePointSource[r.cursor]===A){if(checkIfTwoCodePointsAreAValidEscape(r)){r.advanceCodePoint(),i+=String.fromCodePoint(consumeEscapedCodePoint(o,r));continue}consumeBadURL(o,r);const n=[exports.TokenType.BadURL,r.source.slice(r.representationStart,r.representationEnd+1),r.representationStart,r.representationEnd,void 0];return o.onParseError(new ParseErrorWithToken(e.InvalidEscapeSequenceInURL,r.representationStart,r.representationEnd,["4.3.6. Consume a url token","U+005C REVERSE SOLIDUS (\\)","The input stream does not start with a valid escape sequence"],n)),n}i+=String.fromCodePoint(r.codePointSource[r.cursor]),r.advanceCodePoint()}var s}function consumeIdentLikeToken(e,o){const r=consumeIdentSequence(e,o);if(o.codePointSource[o.cursor]!==m)return[exports.TokenType.Ident,o.source.slice(o.representationStart,o.representationEnd+1),o.representationStart,o.representationEnd,{value:String.fromCodePoint(...r)}];if(checkIfCodePointsMatchURLIdent(r)){o.advanceCodePoint();let t=0;for(;;){const e=isWhitespace(o.codePointSource[o.cursor]),i=isWhitespace(o.codePointSource[o.cursor+1]);if(e&&i){t+=1,o.advanceCodePoint(1);continue}const s=e?o.codePointSource[o.cursor+1]:o.codePointSource[o.cursor];if(s===w||s===n)return t>0&&o.unreadCodePoint(t),[exports.TokenType.Function,o.source.slice(o.representationStart,o.representationEnd+1),o.representationStart,o.representationEnd,{value:String.fromCodePoint(...r)}];break}return consumeUrlToken(e,o)}return o.advanceCodePoint(),[exports.TokenType.Function,o.source.slice(o.representationStart,o.representationEnd+1),o.representationStart,o.representationEnd,{value:String.fromCodePoint(...r)}]}function checkIfThreeCodePointsWouldStartAUnicodeRange(e){return!(e.codePointSource[e.cursor]!==V&&e.codePointSource[e.cursor]!==z||e.codePointSource[e.cursor+1]!==R||e.codePointSource[e.cursor+2]!==_&&!isHexDigitCodePoint(e.codePointSource[e.cursor+2]))}function consumeUnicodeRangeToken(e,o){o.advanceCodePoint(2);const n=[],r=[];for(;void 0!==o.codePointSource[o.cursor]&&n.length<6&&isHexDigitCodePoint(o.codePointSource[o.cursor]);)n.push(o.codePointSource[o.cursor]),o.advanceCodePoint();for(;void 0!==o.codePointSource[o.cursor]&&n.length<6&&o.codePointSource[o.cursor]===_;)0===r.length&&r.push(...n),n.push(j),r.push(Q),o.advanceCodePoint();if(!r.length&&o.codePointSource[o.cursor]===k&&isHexDigitCodePoint(o.codePointSource[o.cursor+1]))for(o.advanceCodePoint();void 0!==o.codePointSource[o.cursor]&&r.length<6&&isHexDigitCodePoint(o.codePointSource[o.cursor]);)r.push(o.codePointSource[o.cursor]),o.advanceCodePoint();if(!r.length){const e=parseInt(String.fromCodePoint(...n),16);return[exports.TokenType.UnicodeRange,o.source.slice(o.representationStart,o.representationEnd+1),o.representationStart,o.representationEnd,{startOfRange:e,endOfRange:e}]}const t=parseInt(String.fromCodePoint(...n),16),i=parseInt(String.fromCodePoint(...r),16);return[exports.TokenType.UnicodeRange,o.source.slice(o.representationStart,o.representationEnd+1),o.representationStart,o.representationEnd,{startOfRange:t,endOfRange:i}]}function tokenizer(o,r){const t=o.css.valueOf(),d=o.unicodeRangesAllowed??!1,p=new Reader(t),S={onParseError:r?.onParseError??noop};return{nextToken:function nextToken(){p.resetRepresentation();const o=p.codePointSource[p.cursor];if(void 0===o)return[exports.TokenType.EOF,"",-1,-1,void 0];if(o===H&&checkIfTwoCodePointsStartAComment(p))return consumeComment(S,p);if(d&&(o===V||o===z)&&checkIfThreeCodePointsWouldStartAUnicodeRange(p))return consumeUnicodeRangeToken(0,p);if(isIdentStartCodePoint(o))return consumeIdentLikeToken(S,p);if(isDigitCodePoint(o))return consumeNumericToken(S,p);switch(o){case a:return p.advanceCodePoint(),[exports.TokenType.Comma,",",p.representationStart,p.representationEnd,void 0];case c:return p.advanceCodePoint(),[exports.TokenType.Colon,":",p.representationStart,p.representationEnd,void 0];case q:return p.advanceCodePoint(),[exports.TokenType.Semicolon,";",p.representationStart,p.representationEnd,void 0];case m:return p.advanceCodePoint(),[exports.TokenType.OpenParen,"(",p.representationStart,p.representationEnd,void 0];case N:return p.advanceCodePoint(),[exports.TokenType.CloseParen,")",p.representationStart,p.representationEnd,void 0];case h:return p.advanceCodePoint(),[exports.TokenType.OpenSquare,"[",p.representationStart,p.representationEnd,void 0];case F:return p.advanceCodePoint(),[exports.TokenType.CloseSquare,"]",p.representationStart,p.representationEnd,void 0];case x:return p.advanceCodePoint(),[exports.TokenType.OpenCurly,"{",p.representationStart,p.representationEnd,void 0];case W:return p.advanceCodePoint(),[exports.TokenType.CloseCurly,"}",p.representationStart,p.representationEnd,void 0];case n:case w:return consumeStringToken(S,p);case O:return consumeHashToken(S,p);case R:case P:return checkIfThreeCodePointsWouldStartANumber(p)?consumeNumericToken(S,p):(p.advanceCodePoint(),[exports.TokenType.Delim,p.source[p.representationStart],p.representationStart,p.representationEnd,{value:p.source[p.representationStart]}]);case y:case i:case T:case s:case B:return consumeWhiteSpace(p);case k:return checkIfThreeCodePointsWouldStartANumber(p)?consumeNumericToken(S,p):checkIfThreeCodePointsWouldStartCDC(p)?(p.advanceCodePoint(3),[exports.TokenType.CDC,"--\x3e",p.representationStart,p.representationEnd,void 0]):checkIfThreeCodePointsWouldStartAnIdentSequence(0,p)?consumeIdentLikeToken(S,p):(p.advanceCodePoint(),[exports.TokenType.Delim,"-",p.representationStart,p.representationEnd,{value:"-"}]);case E:return checkIfFourCodePointsWouldStartCDO(p)?(p.advanceCodePoint(4),[exports.TokenType.CDO,"\x3c!--",p.representationStart,p.representationEnd,void 0]):(p.advanceCodePoint(),[exports.TokenType.Delim,"<",p.representationStart,p.representationEnd,{value:"<"}]);case u:if(p.advanceCodePoint(),checkIfThreeCodePointsWouldStartAnIdentSequence(0,p)){const e=consumeIdentSequence(S,p);return[exports.TokenType.AtKeyword,p.source.slice(p.representationStart,p.representationEnd+1),p.representationStart,p.representationEnd,{value:String.fromCodePoint(...e)}]}return[exports.TokenType.Delim,"@",p.representationStart,p.representationEnd,{value:"@"}];case A:{if(checkIfTwoCodePointsAreAValidEscape(p))return consumeIdentLikeToken(S,p);p.advanceCodePoint();const o=[exports.TokenType.Delim,"\\",p.representationStart,p.representationEnd,{value:"\\"}];return S.onParseError(new ParseErrorWithToken(e.InvalidEscapeSequenceAfterBackslash,p.representationStart,p.representationEnd,["4.3.1. Consume a token","U+005C REVERSE SOLIDUS (\\)","The input stream does not start with a valid escape sequence"],o)),o}}return p.advanceCodePoint(),[exports.TokenType.Delim,p.source[p.representationStart],p.representationStart,p.representationEnd,{value:p.source[p.representationStart]}]},endOfFile:function endOfFile(){return void 0===p.codePointSource[p.cursor]}}}function noop(){}function ensureThatValueRoundTripsAsIdent(e){let o=0;e[0]===k&&e[1]===k?o=2:e[0]===k&&e[1]?(o=2,isIdentStartCodePoint(e[1])||(o+=insertEscapedCodePoint(e,1,e[1]))):isIdentStartCodePoint(e[0])?o=1:(o=1,o+=insertEscapedCodePoint(e,0,e[0]));for(let n=o;n<e.length;n++)isIdentCodePoint(e[n])||(n+=insertEscapedCodePoint(e,n,e[n]));return e}function insertEscapedCodePoint(e,o,n){const r=n.toString(16),t=[];for(const e of r)t.push(e.codePointAt(0));const i=e[o+1];return o===e.length-1||i&&isHexDigitCodePoint(i)?(e.splice(o,1,92,...t,32),1+t.length):(e.splice(o,1,92,...t),t.length)}exports.TokenType=void 0,(G=exports.TokenType||(exports.TokenType={})).Comment="comment",G.AtKeyword="at-keyword-token",G.BadString="bad-string-token",G.BadURL="bad-url-token",G.CDC="CDC-token",G.CDO="CDO-token",G.Colon="colon-token",G.Comma="comma-token",G.Delim="delim-token",G.Dimension="dimension-token",G.EOF="EOF-token",G.Function="function-token",G.Hash="hash-token",G.Ident="ident-token",G.Number="number-token",G.Percentage="percentage-token",G.Semicolon="semicolon-token",G.String="string-token",G.URL="url-token",G.Whitespace="whitespace-token",G.OpenParen="(-token",G.CloseParen=")-token",G.OpenSquare="[-token",G.CloseSquare="]-token",G.OpenCurly="{-token",G.CloseCurly="}-token",G.UnicodeRange="unicode-range-token",exports.NumberType=void 0,(X=exports.NumberType||(exports.NumberType={})).Integer="integer",X.Number="number",exports.HashType=void 0,(Y=exports.HashType||(exports.HashType={})).Unrestricted="unrestricted",Y.ID="id";const Z=Object.values(exports.TokenType);exports.ParseError=ParseError,exports.ParseErrorMessage=e,exports.ParseErrorWithToken=ParseErrorWithToken,exports.Reader=Reader,exports.cloneTokens=function cloneTokens(e){return o?structuredClone(e):JSON.parse(JSON.stringify(e))},exports.isToken=function isToken(e){return!!Array.isArray(e)&&(!(e.length<4)&&(!!Z.includes(e[0])&&("string"==typeof e[1]&&("number"==typeof e[2]&&"number"==typeof e[3]))))},exports.isTokenAtKeyword=function isTokenAtKeyword(e){return!!e&&e[0]===exports.TokenType.AtKeyword},exports.isTokenBadString=function isTokenBadString(e){return!!e&&e[0]===exports.TokenType.BadString},exports.isTokenBadURL=function isTokenBadURL(e){return!!e&&e[0]===exports.TokenType.BadURL},exports.isTokenCDC=function isTokenCDC(e){return!!e&&e[0]===exports.TokenType.CDC},exports.isTokenCDO=function isTokenCDO(e){return!!e&&e[0]===exports.TokenType.CDO},exports.isTokenCloseCurly=function isTokenCloseCurly(e){return!!e&&e[0]===exports.TokenType.CloseCurly},exports.isTokenCloseParen=function isTokenCloseParen(e){return!!e&&e[0]===exports.TokenType.CloseParen},exports.isTokenCloseSquare=function isTokenCloseSquare(e){return!!e&&e[0]===exports.TokenType.CloseSquare},exports.isTokenColon=function isTokenColon(e){return!!e&&e[0]===exports.TokenType.Colon},exports.isTokenComma=function isTokenComma(e){return!!e&&e[0]===exports.TokenType.Comma},exports.isTokenComment=function isTokenComment(e){return!!e&&e[0]===exports.TokenType.Comment},exports.isTokenDelim=function isTokenDelim(e){return!!e&&e[0]===exports.TokenType.Delim},exports.isTokenDimension=function isTokenDimension(e){return!!e&&e[0]===exports.TokenType.Dimension},exports.isTokenEOF=function isTokenEOF(e){return!!e&&e[0]===exports.TokenType.EOF},exports.isTokenFunction=function isTokenFunction(e){return!!e&&e[0]===exports.TokenType.Function},exports.isTokenHash=function isTokenHash(e){return!!e&&e[0]===exports.TokenType.Hash},exports.isTokenIdent=function isTokenIdent(e){return!!e&&e[0]===exports.TokenType.Ident},exports.isTokenNumber=function isTokenNumber(e){return!!e&&e[0]===exports.TokenType.Number},exports.isTokenNumeric=function isTokenNumeric(e){switch(e[0]){case exports.TokenType.Dimension:case exports.TokenType.Number:case exports.TokenType.Percentage:return!0;default:return!1}},exports.isTokenOpenCurly=function isTokenOpenCurly(e){return!!e&&e[0]===exports.TokenType.OpenCurly},exports.isTokenOpenParen=function isTokenOpenParen(e){return!!e&&e[0]===exports.TokenType.OpenParen},exports.isTokenOpenSquare=function isTokenOpenSquare(e){return!!e&&e[0]===exports.TokenType.OpenSquare},exports.isTokenPercentage=function isTokenPercentage(e){return!!e&&e[0]===exports.TokenType.Percentage},exports.isTokenSemicolon=function isTokenSemicolon(e){return!!e&&e[0]===exports.TokenType.Semicolon},exports.isTokenString=function isTokenString(e){return!!e&&e[0]===exports.TokenType.String},exports.isTokenURL=function isTokenURL(e){return!!e&&e[0]===exports.TokenType.URL},exports.isTokenUnicodeRange=function isTokenUnicodeRange(e){return!!e&&e[0]===exports.TokenType.UnicodeRange},exports.isTokenWhiteSpaceOrComment=function isTokenWhiteSpaceOrComment(e){switch(e[0]){case exports.TokenType.Whitespace:case exports.TokenType.Comment:return!0;default:return!1}},exports.isTokenWhitespace=function isTokenWhitespace(e){return!!e&&e[0]===exports.TokenType.Whitespace},exports.mirrorVariant=function mirrorVariant(e){switch(e[0]){case exports.TokenType.OpenParen:return[exports.TokenType.CloseParen,")",-1,-1,void 0];case exports.TokenType.CloseParen:return[exports.TokenType.OpenParen,"(",-1,-1,void 0];case exports.TokenType.OpenCurly:return[exports.TokenType.CloseCurly,"}",-1,-1,void 0];case exports.TokenType.CloseCurly:return[exports.TokenType.OpenCurly,"{",-1,-1,void 0];case exports.TokenType.OpenSquare:return[exports.TokenType.CloseSquare,"]",-1,-1,void 0];case exports.TokenType.CloseSquare:return[exports.TokenType.OpenSquare,"[",-1,-1,void 0];default:return null}},exports.mirrorVariantType=function mirrorVariantType(e){switch(e){case exports.TokenType.OpenParen:return exports.TokenType.CloseParen;case exports.TokenType.CloseParen:return exports.TokenType.OpenParen;case exports.TokenType.OpenCurly:return exports.TokenType.CloseCurly;case exports.TokenType.CloseCurly:return exports.TokenType.OpenCurly;case exports.TokenType.OpenSquare:return exports.TokenType.CloseSquare;case exports.TokenType.CloseSquare:return exports.TokenType.OpenSquare;default:return null}},exports.mutateIdent=function mutateIdent(e,o){const n=[];for(const e of o)n.push(e.codePointAt(0));const r=String.fromCodePoint(...ensureThatValueRoundTripsAsIdent(n));e[1]=r,e[4].value=o},exports.mutateUnit=function mutateUnit(e,o){const n=[];for(const e of o)n.push(e.codePointAt(0));const r=ensureThatValueRoundTripsAsIdent(n);101===r[0]&&insertEscapedCodePoint(r,0,r[0]);const t=String.fromCodePoint(...r),i="+"===e[4].signCharacter?e[4].signCharacter:"",s=e[4].value.toString();e[1]=`${i}${s}${t}`,e[4].unit=o},exports.stringify=function stringify(...e){let o="";for(let n=0;n<e.length;n++)o+=e[n][1];return o},exports.tokenize=function tokenize(e,o){const n=tokenizer(e,o),r=[];{for(;!n.endOfFile();){const e=n.nextToken();e&&r.push(e)}const e=n.nextToken();e&&r.push(e)}return r},exports.tokenizer=tokenizer;
