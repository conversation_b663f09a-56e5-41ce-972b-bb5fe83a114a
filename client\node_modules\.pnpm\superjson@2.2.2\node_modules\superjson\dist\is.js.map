{"version": 3, "file": "is.js", "sourceRoot": "", "sources": ["../src/is.ts"], "names": [], "mappings": "AAAA,MAAM,OAAO,GAAG,CAAC,OAAY,EAAU,EAAE,CACvC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAEvD,MAAM,CAAC,MAAM,WAAW,GAAG,CAAC,OAAY,EAAwB,EAAE,CAChE,OAAO,OAAO,KAAK,WAAW,CAAC;AAEjC,MAAM,CAAC,MAAM,MAAM,GAAG,CAAC,OAAY,EAAmB,EAAE,CAAC,OAAO,KAAK,IAAI,CAAC;AAE1E,MAAM,CAAC,MAAM,aAAa,GAAG,CAC3B,OAAY,EACuB,EAAE;IACrC,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,KAAK,IAAI;QAAE,OAAO,KAAK,CAAC;IAClE,IAAI,OAAO,KAAK,MAAM,CAAC,SAAS;QAAE,OAAO,KAAK,CAAC;IAC/C,IAAI,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,IAAI;QAAE,OAAO,IAAI,CAAC;IAEzD,OAAO,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,MAAM,CAAC,SAAS,CAAC;AAC7D,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,aAAa,GAAG,CAAC,OAAY,EAAiB,EAAE,CAC3D,aAAa,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;AAE9D,MAAM,CAAC,MAAM,OAAO,GAAG,CAAC,OAAY,EAAoB,EAAE,CACxD,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAEzB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAC,OAAY,EAAqB,EAAE,CAC1D,OAAO,OAAO,KAAK,QAAQ,CAAC;AAE9B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAC,OAAY,EAAqB,EAAE,CAC1D,OAAO,OAAO,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AAEjD,MAAM,CAAC,MAAM,SAAS,GAAG,CAAC,OAAY,EAAsB,EAAE,CAC5D,OAAO,OAAO,KAAK,SAAS,CAAC;AAE/B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAC,OAAY,EAAqB,EAAE,CAC1D,OAAO,YAAY,MAAM,CAAC;AAE5B,MAAM,CAAC,MAAM,KAAK,GAAG,CAAC,OAAY,EAA4B,EAAE,CAC9D,OAAO,YAAY,GAAG,CAAC;AAEzB,MAAM,CAAC,MAAM,KAAK,GAAG,CAAC,OAAY,EAAuB,EAAE,CACzD,OAAO,YAAY,GAAG,CAAC;AAEzB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAC,OAAY,EAAqB,EAAE,CAC1D,OAAO,CAAC,OAAO,CAAC,KAAK,QAAQ,CAAC;AAEhC,MAAM,CAAC,MAAM,MAAM,GAAG,CAAC,OAAY,EAAmB,EAAE,CACtD,OAAO,YAAY,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;AAEvD,MAAM,CAAC,MAAM,OAAO,GAAG,CAAC,OAAY,EAAoB,EAAE,CACxD,OAAO,YAAY,KAAK,CAAC;AAE3B,MAAM,CAAC,MAAM,UAAU,GAAG,CAAC,OAAY,EAAyB,EAAE,CAChE,OAAO,OAAO,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;AAEhD,MAAM,CAAC,MAAM,WAAW,GAAG,CACzB,OAAY,EACsD,EAAE,CACpE,SAAS,CAAC,OAAO,CAAC;IAClB,MAAM,CAAC,OAAO,CAAC;IACf,WAAW,CAAC,OAAO,CAAC;IACpB,QAAQ,CAAC,OAAO,CAAC;IACjB,QAAQ,CAAC,OAAO,CAAC;IACjB,QAAQ,CAAC,OAAO,CAAC,CAAC;AAEpB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAC,OAAY,EAAqB,EAAE,CAC1D,OAAO,OAAO,KAAK,QAAQ,CAAC;AAE9B,MAAM,CAAC,MAAM,UAAU,GAAG,CAAC,OAAY,EAAqB,EAAE,CAC5D,OAAO,KAAK,QAAQ,IAAI,OAAO,KAAK,CAAC,QAAQ,CAAC;AAehD,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,OAAY,EAAyB,EAAE,CAClE,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,YAAY,QAAQ,CAAC,CAAC;AAEhE,MAAM,CAAC,MAAM,KAAK,GAAG,CAAC,OAAY,EAAkB,EAAE,CAAC,OAAO,YAAY,GAAG,CAAC"}