{"hash": "71888191", "configHash": "ea42d64a", "lockfileHash": "fb36c<PERSON>", "browserHash": "c6e04da5", "optimized": {"pinia-plugin-persistedstate": {"src": "../../../../.pnpm/pinia-plugin-persistedstate_cc5121dd2b8c5c58d5f461b4fd0fbea0/node_modules/pinia-plugin-persistedstate/dist/index.js", "file": "pinia-plugin-persistedstate.js", "fileHash": "3f2b3ea3", "needsInterop": false}, "@intlify/shared": {"src": "../../../../.pnpm/@intlify+shared@10.0.7/node_modules/@intlify/shared/dist/shared.mjs", "file": "@intlify_shared.js", "fileHash": "a92e6409", "needsInterop": false}, "@intlify/core-base": {"src": "../../../../.pnpm/@intlify+core-base@10.0.7/node_modules/@intlify/core-base/dist/core-base.mjs", "file": "@intlify_core-base.js", "fileHash": "80eb36f6", "needsInterop": false}}, "chunks": {"chunk-SKD2BCNZ": {"file": "chunk-SKD2BCNZ.js"}}}