hoistPattern:
  - '*'
hoistedDependencies:
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@antfu/install-pkg@1.1.0':
    '@antfu/install-pkg': private
  '@antfu/utils@8.1.1':
    '@antfu/utils': private
  '@apidevtools/json-schema-ref-parser@11.9.3':
    '@apidevtools/json-schema-ref-parser': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.27.5':
    '@babel/compat-data': private
  '@babel/core@7.27.4':
    '@babel/core': private
  '@babel/generator@7.27.5':
    '@babel/generator': private
  '@babel/helper-annotate-as-pure@7.27.3':
    '@babel/helper-annotate-as-pure': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-create-class-features-plugin@7.27.1(@babel/core@7.27.4)':
    '@babel/helper-create-class-features-plugin': private
  '@babel/helper-member-expression-to-functions@7.27.1':
    '@babel/helper-member-expression-to-functions': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.27.4)':
    '@babel/helper-module-transforms': private
  '@babel/helper-optimise-call-expression@7.27.1':
    '@babel/helper-optimise-call-expression': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-replace-supers@7.27.1(@babel/core@7.27.4)':
    '@babel/helper-replace-supers': private
  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    '@babel/helper-skip-transparent-expression-wrappers': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.27.6':
    '@babel/helpers': private
  '@babel/parser@7.27.5':
    '@babel/parser': private
  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-syntax-jsx': private
  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-syntax-typescript': private
  '@babel/plugin-transform-typescript@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-typescript': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.27.4':
    '@babel/traverse': private
  '@babel/types@7.27.6':
    '@babel/types': private
  '@capsizecss/metrics@3.5.0':
    '@capsizecss/metrics': private
  '@capsizecss/unpack@2.4.0':
    '@capsizecss/unpack': private
  '@clack/core@0.4.2':
    '@clack/core': private
  '@clack/prompts@0.10.1':
    '@clack/prompts': private
  '@cloudflare/kv-asset-handler@0.4.0':
    '@cloudflare/kv-asset-handler': private
  '@colors/colors@1.6.0':
    '@colors/colors': private
  '@dabh/diagnostics@2.0.3':
    '@dabh/diagnostics': private
  '@dependents/detective-less@5.0.1':
    '@dependents/detective-less': private
  '@es-joy/jsdoccomment@0.50.2':
    '@es-joy/jsdoccomment': private
  '@esbuild/aix-ppc64@0.25.5':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.5':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.5':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.5':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.5':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.5':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.5':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.5':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.5':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.5':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.5':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.5':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.5':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.5':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.5':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.5':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.5':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.5':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.5':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.5':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.5':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.25.5':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.5':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.5':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.5':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.29.0(jiti@2.4.2))':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/compat@1.3.0(eslint@9.29.0(jiti@2.4.2))':
    '@eslint/compat': private
  '@eslint/config-array@0.20.1':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.2.3':
    '@eslint/config-helpers': private
  '@eslint/config-inspector@1.1.0(eslint@9.29.0(jiti@2.4.2))':
    '@eslint/config-inspector': private
  '@eslint/core@0.14.0':
    '@eslint/core': private
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': private
  '@eslint/js@9.29.0':
    '@eslint/js': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.3.2':
    '@eslint/plugin-kit': private
  '@fastify/accept-negotiator@1.1.0':
    '@fastify/accept-negotiator': private
  '@fastify/busboy@3.1.1':
    '@fastify/busboy': private
  '@floating-ui/core@1.7.1':
    '@floating-ui/core': private
  '@floating-ui/dom@1.7.1':
    '@floating-ui/dom': private
  '@floating-ui/utils@0.2.9':
    '@floating-ui/utils': private
  '@floating-ui/vue@1.1.6(vue@3.5.16(typescript@5.8.3))':
    '@floating-ui/vue': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@iconify/collections@1.0.559':
    '@iconify/collections': private
  '@iconify/types@2.0.0':
    '@iconify/types': private
  '@iconify/utils@2.3.0':
    '@iconify/utils': private
  '@iconify/vue@5.0.0(vue@3.5.16(typescript@5.8.3))':
    '@iconify/vue': private
  '@internationalized/date@3.8.2':
    '@internationalized/date': private
  '@internationalized/number@3.6.3':
    '@internationalized/number': private
  '@intlify/bundle-utils@10.0.1(vue-i18n@10.0.7(vue@3.5.16(typescript@5.8.3)))':
    '@intlify/bundle-utils': private
  '@intlify/core-base@10.0.7':
    '@intlify/core-base': private
  '@intlify/core@10.0.7':
    '@intlify/core': private
  '@intlify/h3@0.6.1':
    '@intlify/h3': private
  '@intlify/message-compiler@11.1.5':
    '@intlify/message-compiler': private
  '@intlify/shared@10.0.7':
    '@intlify/shared': private
  '@intlify/unplugin-vue-i18n@6.0.8(@vue/compiler-dom@3.5.16)(eslint@9.29.0(jiti@2.4.2))(rollup@4.43.0)(typescript@5.8.3)(vue-i18n@10.0.7(vue@3.5.16(typescript@5.8.3)))(vue@3.5.16(typescript@5.8.3))':
    '@intlify/unplugin-vue-i18n': private
  '@intlify/utils@0.13.0':
    '@intlify/utils': private
  '@intlify/vue-i18n-extensions@8.0.0(@intlify/shared@11.1.5)(@vue/compiler-dom@3.5.16)(vue-i18n@10.0.7(vue@3.5.16(typescript@5.8.3)))(vue@3.5.16(typescript@5.8.3))':
    '@intlify/vue-i18n-extensions': private
  '@ioredis/commands@1.2.0':
    '@ioredis/commands': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/source-map@0.3.6':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@jsdevtools/ono@7.1.3':
    '@jsdevtools/ono': private
  '@kwsites/file-exists@1.1.1':
    '@kwsites/file-exists': private
  '@kwsites/promise-deferred@1.1.1':
    '@kwsites/promise-deferred': private
  '@mapbox/node-pre-gyp@2.0.0':
    '@mapbox/node-pre-gyp': private
  '@miyaneee/rollup-plugin-json5@1.2.0(rollup@4.43.0)':
    '@miyaneee/rollup-plugin-json5': private
  '@netlify/binary-info@1.0.0':
    '@netlify/binary-info': private
  '@netlify/blobs@9.1.2':
    '@netlify/blobs': private
  '@netlify/dev-utils@2.2.0':
    '@netlify/dev-utils': private
  '@netlify/functions@3.1.10(rollup@4.43.0)':
    '@netlify/functions': private
  '@netlify/open-api@2.37.0':
    '@netlify/open-api': private
  '@netlify/runtime-utils@1.3.1':
    '@netlify/runtime-utils': private
  '@netlify/serverless-functions-api@1.41.2':
    '@netlify/serverless-functions-api': private
  '@netlify/zip-it-and-ship-it@12.1.4(rollup@4.43.0)':
    '@netlify/zip-it-and-ship-it': private
  '@nodelib/fs.scandir@4.0.1':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@4.0.0':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@3.0.1':
    '@nodelib/fs.walk': private
  '@nuxt/cli@3.25.1(magicast@0.3.5)':
    '@nuxt/cli': private
  '@nuxt/devalue@2.0.2':
    '@nuxt/devalue': private
  '@nuxt/devtools-kit@2.5.0(magicast@0.3.5)(vite@6.3.5(@types/node@24.0.2)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.42.0)(yaml@2.8.0))':
    '@nuxt/devtools-kit': private
  '@nuxt/devtools-wizard@2.5.0':
    '@nuxt/devtools-wizard': private
  '@nuxt/devtools@2.5.0(vite@6.3.5(@types/node@24.0.2)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.42.0)(yaml@2.8.0))(vue@3.5.16(typescript@5.8.3))':
    '@nuxt/devtools': private
  '@nuxt/eslint-config@1.4.1(@typescript-eslint/utils@8.34.0(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3))(@vue/compiler-sfc@3.5.16)(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3)':
    '@nuxt/eslint-config': private
  '@nuxt/eslint-plugin@1.4.1(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3)':
    '@nuxt/eslint-plugin': private
  '@nuxt/kit@3.17.5(magicast@0.3.5)':
    '@nuxt/kit': private
  '@nuxt/schema@3.17.5':
    '@nuxt/schema': private
  '@nuxt/telemetry@2.6.6(magicast@0.3.5)':
    '@nuxt/telemetry': private
  '@nuxt/vite-builder@3.17.5(@types/node@24.0.2)(eslint@9.29.0(jiti@2.4.2))(lightningcss@1.30.1)(magicast@0.3.5)(optionator@0.9.4)(rollup@4.43.0)(terser@5.42.0)(typescript@5.8.3)(vue@3.5.16(typescript@5.8.3))(yaml@2.8.0)':
    '@nuxt/vite-builder': private
  '@nuxtjs/color-mode@3.5.2(magicast@0.3.5)':
    '@nuxtjs/color-mode': private
  '@oxc-parser/binding-darwin-arm64@0.70.0':
    '@oxc-parser/binding-darwin-arm64': private
  '@oxc-parser/binding-darwin-x64@0.70.0':
    '@oxc-parser/binding-darwin-x64': private
  '@oxc-parser/binding-freebsd-x64@0.70.0':
    '@oxc-parser/binding-freebsd-x64': private
  '@oxc-parser/binding-linux-arm-gnueabihf@0.70.0':
    '@oxc-parser/binding-linux-arm-gnueabihf': private
  '@oxc-parser/binding-linux-arm-musleabihf@0.70.0':
    '@oxc-parser/binding-linux-arm-musleabihf': private
  '@oxc-parser/binding-linux-arm64-gnu@0.70.0':
    '@oxc-parser/binding-linux-arm64-gnu': private
  '@oxc-parser/binding-linux-arm64-musl@0.70.0':
    '@oxc-parser/binding-linux-arm64-musl': private
  '@oxc-parser/binding-linux-riscv64-gnu@0.70.0':
    '@oxc-parser/binding-linux-riscv64-gnu': private
  '@oxc-parser/binding-linux-s390x-gnu@0.70.0':
    '@oxc-parser/binding-linux-s390x-gnu': private
  '@oxc-parser/binding-linux-x64-gnu@0.70.0':
    '@oxc-parser/binding-linux-x64-gnu': private
  '@oxc-parser/binding-linux-x64-musl@0.70.0':
    '@oxc-parser/binding-linux-x64-musl': private
  '@oxc-parser/binding-wasm32-wasi@0.70.0':
    '@oxc-parser/binding-wasm32-wasi': private
  '@oxc-parser/binding-win32-arm64-msvc@0.70.0':
    '@oxc-parser/binding-win32-arm64-msvc': private
  '@oxc-parser/binding-win32-x64-msvc@0.70.0':
    '@oxc-parser/binding-win32-x64-msvc': private
  '@oxc-parser/wasm@0.60.0':
    '@oxc-parser/wasm': private
  '@oxc-project/types@0.60.0':
    '@oxc-project/types': private
  '@parcel/watcher-android-arm64@2.5.1':
    '@parcel/watcher-android-arm64': private
  '@parcel/watcher-darwin-arm64@2.5.1':
    '@parcel/watcher-darwin-arm64': private
  '@parcel/watcher-darwin-x64@2.5.1':
    '@parcel/watcher-darwin-x64': private
  '@parcel/watcher-freebsd-x64@2.5.1':
    '@parcel/watcher-freebsd-x64': private
  '@parcel/watcher-linux-arm-glibc@2.5.1':
    '@parcel/watcher-linux-arm-glibc': private
  '@parcel/watcher-linux-arm-musl@2.5.1':
    '@parcel/watcher-linux-arm-musl': private
  '@parcel/watcher-linux-arm64-glibc@2.5.1':
    '@parcel/watcher-linux-arm64-glibc': private
  '@parcel/watcher-linux-arm64-musl@2.5.1':
    '@parcel/watcher-linux-arm64-musl': private
  '@parcel/watcher-linux-x64-glibc@2.5.1':
    '@parcel/watcher-linux-x64-glibc': private
  '@parcel/watcher-linux-x64-musl@2.5.1':
    '@parcel/watcher-linux-x64-musl': private
  '@parcel/watcher-wasm@2.5.1':
    '@parcel/watcher-wasm': private
  '@parcel/watcher-win32-arm64@2.5.1':
    '@parcel/watcher-win32-arm64': private
  '@parcel/watcher-win32-ia32@2.5.1':
    '@parcel/watcher-win32-ia32': private
  '@parcel/watcher-win32-x64@2.5.1':
    '@parcel/watcher-win32-x64': private
  '@parcel/watcher@2.5.1':
    '@parcel/watcher': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@polka/url@1.0.0-next.29':
    '@polka/url': private
  '@poppinss/colors@4.1.4':
    '@poppinss/colors': private
  '@poppinss/dumper@0.6.3':
    '@poppinss/dumper': private
  '@poppinss/exception@1.2.1':
    '@poppinss/exception': private
  '@rolldown/pluginutils@1.0.0-beta.16':
    '@rolldown/pluginutils': private
  '@rollup/plugin-alias@5.1.1(rollup@4.43.0)':
    '@rollup/plugin-alias': private
  '@rollup/plugin-commonjs@28.0.5(rollup@4.43.0)':
    '@rollup/plugin-commonjs': private
  '@rollup/plugin-inject@5.0.5(rollup@4.43.0)':
    '@rollup/plugin-inject': private
  '@rollup/plugin-json@6.1.0(rollup@4.43.0)':
    '@rollup/plugin-json': private
  '@rollup/plugin-node-resolve@16.0.1(rollup@4.43.0)':
    '@rollup/plugin-node-resolve': private
  '@rollup/plugin-replace@6.0.2(rollup@4.43.0)':
    '@rollup/plugin-replace': private
  '@rollup/plugin-terser@0.4.4(rollup@4.43.0)':
    '@rollup/plugin-terser': private
  '@rollup/plugin-yaml@4.1.2(rollup@4.43.0)':
    '@rollup/plugin-yaml': private
  '@rollup/pluginutils@5.1.4(rollup@4.43.0)':
    '@rollup/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.43.0':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.43.0':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.43.0':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.43.0':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.43.0':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.43.0':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.43.0':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.43.0':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.43.0':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.43.0':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.43.0':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.43.0':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.43.0':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.43.0':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.43.0':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.43.0':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.43.0':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.43.0':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.43.0':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.43.0':
    '@rollup/rollup-win32-x64-msvc': private
  '@sindresorhus/is@7.0.2':
    '@sindresorhus/is': private
  '@sindresorhus/merge-streams@2.3.0':
    '@sindresorhus/merge-streams': private
  '@speed-highlight/core@1.2.7':
    '@speed-highlight/core': private
  '@standard-schema/spec@1.0.0':
    '@standard-schema/spec': private
  '@stylistic/eslint-plugin@4.4.1(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3)':
    '@stylistic/eslint-plugin': private
  '@swc/helpers@0.5.17':
    '@swc/helpers': private
  '@tailwindcss/node@4.1.10':
    '@tailwindcss/node': private
  '@tailwindcss/oxide-android-arm64@4.1.10':
    '@tailwindcss/oxide-android-arm64': private
  '@tailwindcss/oxide-darwin-arm64@4.1.10':
    '@tailwindcss/oxide-darwin-arm64': private
  '@tailwindcss/oxide-darwin-x64@4.1.10':
    '@tailwindcss/oxide-darwin-x64': private
  '@tailwindcss/oxide-freebsd-x64@4.1.10':
    '@tailwindcss/oxide-freebsd-x64': private
  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.10':
    '@tailwindcss/oxide-linux-arm-gnueabihf': private
  '@tailwindcss/oxide-linux-arm64-gnu@4.1.10':
    '@tailwindcss/oxide-linux-arm64-gnu': private
  '@tailwindcss/oxide-linux-arm64-musl@4.1.10':
    '@tailwindcss/oxide-linux-arm64-musl': private
  '@tailwindcss/oxide-linux-x64-gnu@4.1.10':
    '@tailwindcss/oxide-linux-x64-gnu': private
  '@tailwindcss/oxide-linux-x64-musl@4.1.10':
    '@tailwindcss/oxide-linux-x64-musl': private
  '@tailwindcss/oxide-wasm32-wasi@4.1.10':
    '@tailwindcss/oxide-wasm32-wasi': private
  '@tailwindcss/oxide-win32-arm64-msvc@4.1.10':
    '@tailwindcss/oxide-win32-arm64-msvc': private
  '@tailwindcss/oxide-win32-x64-msvc@4.1.10':
    '@tailwindcss/oxide-win32-x64-msvc': private
  '@tailwindcss/oxide@4.1.10':
    '@tailwindcss/oxide': private
  '@tailwindcss/postcss@4.1.10':
    '@tailwindcss/postcss': private
  '@tailwindcss/vite@4.1.10(vite@6.3.5(@types/node@24.0.2)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.42.0)(yaml@2.8.0))':
    '@tailwindcss/vite': private
  '@tanstack/table-core@8.21.3':
    '@tanstack/table-core': private
  '@tanstack/virtual-core@3.13.10':
    '@tanstack/virtual-core': private
  '@tanstack/vue-table@8.21.3(vue@3.5.16(typescript@5.8.3))':
    '@tanstack/vue-table': private
  '@tanstack/vue-virtual@3.13.10(vue@3.5.16(typescript@5.8.3))':
    '@tanstack/vue-virtual': private
  '@trysound/sax@0.2.0':
    '@trysound/sax': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/node@24.0.2':
    '@types/node': private
  '@types/normalize-package-data@2.4.4':
    '@types/normalize-package-data': private
  '@types/parse-path@7.1.0':
    '@types/parse-path': private
  '@types/resolve@1.20.2':
    '@types/resolve': private
  '@types/triple-beam@1.3.5':
    '@types/triple-beam': private
  '@types/web-bluetooth@0.0.21':
    '@types/web-bluetooth': private
  '@types/yauzl@2.10.3':
    '@types/yauzl': private
  '@typescript-eslint/eslint-plugin@8.34.0(@typescript-eslint/parser@8.34.0(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/eslint-plugin': private
  '@typescript-eslint/parser@8.34.0(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/project-service@8.34.0(typescript@5.8.3)':
    '@typescript-eslint/project-service': private
  '@typescript-eslint/scope-manager@8.34.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/tsconfig-utils@8.34.0(typescript@5.8.3)':
    '@typescript-eslint/tsconfig-utils': private
  '@typescript-eslint/type-utils@8.34.0(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.34.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.34.0(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.34.0(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.34.0':
    '@typescript-eslint/visitor-keys': private
  '@unhead/vue@2.0.10(vue@3.5.16(typescript@5.8.3))':
    '@unhead/vue': private
  '@unrs/resolver-binding-android-arm-eabi@1.9.0':
    '@unrs/resolver-binding-android-arm-eabi': private
  '@unrs/resolver-binding-android-arm64@1.9.0':
    '@unrs/resolver-binding-android-arm64': private
  '@unrs/resolver-binding-darwin-arm64@1.9.0':
    '@unrs/resolver-binding-darwin-arm64': private
  '@unrs/resolver-binding-darwin-x64@1.9.0':
    '@unrs/resolver-binding-darwin-x64': private
  '@unrs/resolver-binding-freebsd-x64@1.9.0':
    '@unrs/resolver-binding-freebsd-x64': private
  '@unrs/resolver-binding-linux-arm-gnueabihf@1.9.0':
    '@unrs/resolver-binding-linux-arm-gnueabihf': private
  '@unrs/resolver-binding-linux-arm-musleabihf@1.9.0':
    '@unrs/resolver-binding-linux-arm-musleabihf': private
  '@unrs/resolver-binding-linux-arm64-gnu@1.9.0':
    '@unrs/resolver-binding-linux-arm64-gnu': private
  '@unrs/resolver-binding-linux-arm64-musl@1.9.0':
    '@unrs/resolver-binding-linux-arm64-musl': private
  '@unrs/resolver-binding-linux-ppc64-gnu@1.9.0':
    '@unrs/resolver-binding-linux-ppc64-gnu': private
  '@unrs/resolver-binding-linux-riscv64-gnu@1.9.0':
    '@unrs/resolver-binding-linux-riscv64-gnu': private
  '@unrs/resolver-binding-linux-riscv64-musl@1.9.0':
    '@unrs/resolver-binding-linux-riscv64-musl': private
  '@unrs/resolver-binding-linux-s390x-gnu@1.9.0':
    '@unrs/resolver-binding-linux-s390x-gnu': private
  '@unrs/resolver-binding-linux-x64-gnu@1.9.0':
    '@unrs/resolver-binding-linux-x64-gnu': private
  '@unrs/resolver-binding-linux-x64-musl@1.9.0':
    '@unrs/resolver-binding-linux-x64-musl': private
  '@unrs/resolver-binding-wasm32-wasi@1.9.0':
    '@unrs/resolver-binding-wasm32-wasi': private
  '@unrs/resolver-binding-win32-arm64-msvc@1.9.0':
    '@unrs/resolver-binding-win32-arm64-msvc': private
  '@unrs/resolver-binding-win32-ia32-msvc@1.9.0':
    '@unrs/resolver-binding-win32-ia32-msvc': private
  '@unrs/resolver-binding-win32-x64-msvc@1.9.0':
    '@unrs/resolver-binding-win32-x64-msvc': private
  '@vercel/nft@0.29.4(rollup@4.43.0)':
    '@vercel/nft': private
  '@vitejs/plugin-vue-jsx@4.2.0(vite@6.3.5(@types/node@24.0.2)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.42.0)(yaml@2.8.0))(vue@3.5.16(typescript@5.8.3))':
    '@vitejs/plugin-vue-jsx': private
  '@vitejs/plugin-vue@5.2.4(vite@6.3.5(@types/node@24.0.2)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.42.0)(yaml@2.8.0))(vue@3.5.16(typescript@5.8.3))':
    '@vitejs/plugin-vue': private
  '@vue-macros/common@1.16.1(vue@3.5.16(typescript@5.8.3))':
    '@vue-macros/common': private
  '@vue/babel-helper-vue-transform-on@1.4.0':
    '@vue/babel-helper-vue-transform-on': private
  '@vue/babel-plugin-jsx@1.4.0(@babel/core@7.27.4)':
    '@vue/babel-plugin-jsx': private
  '@vue/babel-plugin-resolve-type@1.4.0(@babel/core@7.27.4)':
    '@vue/babel-plugin-resolve-type': private
  '@vue/compiler-core@3.5.16':
    '@vue/compiler-core': private
  '@vue/compiler-dom@3.5.16':
    '@vue/compiler-dom': private
  '@vue/compiler-sfc@3.5.16':
    '@vue/compiler-sfc': private
  '@vue/compiler-ssr@3.5.16':
    '@vue/compiler-ssr': private
  '@vue/devtools-api@7.7.7':
    '@vue/devtools-api': private
  '@vue/devtools-core@7.7.7(vite@6.3.5(@types/node@24.0.2)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.42.0)(yaml@2.8.0))(vue@3.5.16(typescript@5.8.3))':
    '@vue/devtools-core': private
  '@vue/devtools-kit@7.7.7':
    '@vue/devtools-kit': private
  '@vue/devtools-shared@7.7.7':
    '@vue/devtools-shared': private
  '@vue/reactivity@3.5.16':
    '@vue/reactivity': private
  '@vue/runtime-core@3.5.16':
    '@vue/runtime-core': private
  '@vue/runtime-dom@3.5.16':
    '@vue/runtime-dom': private
  '@vue/server-renderer@3.5.16(vue@3.5.16(typescript@5.8.3))':
    '@vue/server-renderer': private
  '@vue/shared@3.5.16':
    '@vue/shared': private
  '@vueuse/core@13.3.0(vue@3.5.16(typescript@5.8.3))':
    '@vueuse/core': private
  '@vueuse/integrations@13.3.0(fuse.js@7.1.0)(jwt-decode@4.0.0)(vue@3.5.16(typescript@5.8.3))':
    '@vueuse/integrations': private
  '@vueuse/metadata@13.3.0':
    '@vueuse/metadata': private
  '@vueuse/shared@13.3.0(vue@3.5.16(typescript@5.8.3))':
    '@vueuse/shared': private
  '@whatwg-node/disposablestack@0.0.6':
    '@whatwg-node/disposablestack': private
  '@whatwg-node/fetch@0.10.8':
    '@whatwg-node/fetch': private
  '@whatwg-node/node-fetch@0.7.21':
    '@whatwg-node/node-fetch': private
  '@whatwg-node/promise-helpers@1.3.2':
    '@whatwg-node/promise-helpers': private
  '@whatwg-node/server@0.9.71':
    '@whatwg-node/server': private
  abbrev@3.0.1:
    abbrev: private
  abort-controller@3.0.0:
    abort-controller: private
  acorn-import-attributes@1.9.5(acorn@8.15.0):
    acorn-import-attributes: private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn@8.15.0:
    acorn: private
  agent-base@7.1.3:
    agent-base: private
  ajv@6.12.6:
    ajv: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  ansis@4.1.0:
    ansis: private
  anymatch@3.1.3:
    anymatch: private
  archiver-utils@5.0.2:
    archiver-utils: private
  archiver@7.0.1:
    archiver: private
  are-docs-informative@0.0.2:
    are-docs-informative: private
  argparse@2.0.1:
    argparse: private
  aria-hidden@1.2.6:
    aria-hidden: private
  ast-kit@1.4.3:
    ast-kit: private
  ast-module-types@6.0.1:
    ast-module-types: private
  ast-walker-scope@0.6.2:
    ast-walker-scope: private
  async-sema@3.1.1:
    async-sema: private
  async@3.2.6:
    async: private
  b4a@1.6.7:
    b4a: private
  balanced-match@1.0.2:
    balanced-match: private
  bare-events@2.5.4:
    bare-events: private
  bare-fs@4.1.5:
    bare-fs: private
  bare-os@3.6.1:
    bare-os: private
  bare-path@3.0.0:
    bare-path: private
  bare-stream@2.6.5(bare-events@2.5.4):
    bare-stream: private
  base64-js@1.5.1:
    base64-js: private
  binary-extensions@2.3.0:
    binary-extensions: private
  bindings@1.5.0:
    bindings: private
  birpc@2.4.0:
    birpc: private
  bl@4.1.0:
    bl: private
  blob-to-buffer@1.2.9:
    blob-to-buffer: private
  boolbase@1.0.0:
    boolbase: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  brotli@1.3.3:
    brotli: private
  browserslist@4.25.0:
    browserslist: private
  buffer-crc32@1.0.0:
    buffer-crc32: private
  buffer-from@1.1.2:
    buffer-from: private
  buffer@6.0.3:
    buffer: private
  builtin-modules@5.0.0:
    builtin-modules: private
  bundle-name@4.1.0:
    bundle-name: private
  bundle-require@5.1.0(esbuild@0.25.5):
    bundle-require: private
  c12@3.0.4(magicast@0.3.5):
    c12: private
  cac@6.7.14:
    cac: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bound@1.0.4:
    call-bound: private
  callsite@1.0.0:
    callsite: private
  callsites@3.1.0:
    callsites: private
  caniuse-api@3.0.0:
    caniuse-api: private
  caniuse-lite@1.0.30001723:
    caniuse-lite: private
  chalk@4.1.2:
    chalk: private
  chokidar@4.0.3:
    chokidar: private
  chownr@3.0.0:
    chownr: private
  ci-info@4.2.0:
    ci-info: private
  citty@0.1.6:
    citty: private
  clean-regexp@1.0.0:
    clean-regexp: private
  clipboardy@4.0.0:
    clipboardy: private
  cliui@8.0.1:
    cliui: private
  clone@2.1.2:
    clone: private
  cluster-key-slot@1.1.2:
    cluster-key-slot: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  colord@2.9.3:
    colord: private
  colorspace@1.1.4:
    colorspace: private
  colortranslator@4.1.0:
    colortranslator: private
  commander@7.2.0:
    commander: private
  comment-parser@1.4.1:
    comment-parser: private
  common-path-prefix@3.0.0:
    common-path-prefix: private
  commondir@1.0.1:
    commondir: private
  compatx@0.2.0:
    compatx: private
  compress-commons@6.0.2:
    compress-commons: private
  concat-map@0.0.1:
    concat-map: private
  confbox@0.2.2:
    confbox: private
  consola@3.4.2:
    consola: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie-es@2.0.0:
    cookie-es: private
  cookie@1.0.2:
    cookie: private
  copy-anything@3.0.5:
    copy-anything: private
  copy-file@11.0.0:
    copy-file: private
  core-js-compat@3.43.0:
    core-js-compat: private
  core-util-is@1.0.3:
    core-util-is: private
  crc-32@1.2.2:
    crc-32: private
  crc32-stream@6.0.0:
    crc32-stream: private
  cron-parser@4.9.0:
    cron-parser: private
  croner@9.0.0:
    croner: private
  cross-fetch@3.2.0:
    cross-fetch: private
  cross-spawn@7.0.6:
    cross-spawn: private
  crossws@0.3.5:
    crossws: private
  css-declaration-sorter@7.2.0(postcss@8.5.5):
    css-declaration-sorter: private
  css-select@5.1.0:
    css-select: private
  css-tree@3.1.0:
    css-tree: private
  css-what@6.1.0:
    css-what: private
  cssesc@3.0.0:
    cssesc: private
  cssfilter@0.0.10:
    cssfilter: private
  cssnano-preset-default@7.0.7(postcss@8.5.5):
    cssnano-preset-default: private
  cssnano-utils@5.0.1(postcss@8.5.5):
    cssnano-utils: private
  cssnano@7.0.7(postcss@8.5.5):
    cssnano: private
  csso@5.0.5:
    csso: private
  csstype@3.1.3:
    csstype: private
  data-uri-to-buffer@4.0.1:
    data-uri-to-buffer: private
  db0@0.3.2:
    db0: private
  debug@4.4.1:
    debug: private
  decache@4.6.2:
    decache: private
  decompress-response@6.0.0:
    decompress-response: private
  deep-extend@0.6.0:
    deep-extend: private
  deep-is@0.1.4:
    deep-is: private
  deep-pick-omit@1.2.1:
    deep-pick-omit: private
  deepmerge@4.3.1:
    deepmerge: private
  default-browser-id@5.0.0:
    default-browser-id: private
  default-browser@5.2.1:
    default-browser: private
  define-lazy-prop@3.0.0:
    define-lazy-prop: private
  defu@6.1.4:
    defu: private
  denque@2.1.0:
    denque: private
  depd@2.0.0:
    depd: private
  destr@2.0.5:
    destr: private
  detect-libc@1.0.3:
    detect-libc: private
  detective-amd@6.0.1:
    detective-amd: private
  detective-cjs@6.0.1:
    detective-cjs: private
  detective-es6@5.0.1:
    detective-es6: private
  detective-postcss@7.0.1(postcss@8.5.5):
    detective-postcss: private
  detective-sass@6.0.1:
    detective-sass: private
  detective-scss@5.0.1:
    detective-scss: private
  detective-stylus@5.0.1:
    detective-stylus: private
  detective-typescript@14.0.0(typescript@5.8.3):
    detective-typescript: private
  detective-vue2@2.2.0(typescript@5.8.3):
    detective-vue2: private
  devalue@5.1.1:
    devalue: private
  dfa@1.2.0:
    dfa: private
  diff@8.0.2:
    diff: private
  dom-serializer@2.0.0:
    dom-serializer: private
  domelementtype@2.3.0:
    domelementtype: private
  domhandler@5.0.3:
    domhandler: private
  domutils@3.2.2:
    domutils: private
  dot-prop@9.0.0:
    dot-prop: private
  dotenv@16.5.0:
    dotenv: private
  dunder-proto@1.0.1:
    dunder-proto: private
  duplexer@0.1.2:
    duplexer: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ee-first@1.1.1:
    ee-first: private
  electron-to-chromium@1.5.167:
    electron-to-chromium: private
  embla-carousel-auto-height@8.6.0(embla-carousel@8.6.0):
    embla-carousel-auto-height: private
  embla-carousel-auto-scroll@8.6.0(embla-carousel@8.6.0):
    embla-carousel-auto-scroll: private
  embla-carousel-autoplay@8.6.0(embla-carousel@8.6.0):
    embla-carousel-autoplay: private
  embla-carousel-class-names@8.6.0(embla-carousel@8.6.0):
    embla-carousel-class-names: private
  embla-carousel-fade@8.6.0(embla-carousel@8.6.0):
    embla-carousel-fade: private
  embla-carousel-reactive-utils@8.6.0(embla-carousel@8.6.0):
    embla-carousel-reactive-utils: private
  embla-carousel-vue@8.6.0(vue@3.5.16(typescript@5.8.3)):
    embla-carousel-vue: private
  embla-carousel-wheel-gestures@8.0.2(embla-carousel@8.6.0):
    embla-carousel-wheel-gestures: private
  embla-carousel@8.6.0:
    embla-carousel: private
  emoji-regex@8.0.0:
    emoji-regex: private
  enabled@2.0.0:
    enabled: private
  encodeurl@2.0.0:
    encodeurl: private
  end-of-stream@1.4.4:
    end-of-stream: private
  enhanced-resolve@5.18.1:
    enhanced-resolve: private
  entities@4.5.0:
    entities: private
  env-paths@3.0.0:
    env-paths: private
  error-stack-parser-es@1.0.5:
    error-stack-parser-es: private
  errx@0.1.0:
    errx: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  esbuild@0.25.5:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  escodegen@2.1.0:
    escodegen: private
  eslint-config-flat-gitignore@2.1.0(eslint@9.29.0(jiti@2.4.2)):
    eslint-config-flat-gitignore: private
  eslint-flat-config-utils@2.1.0:
    eslint-flat-config-utils: private
  eslint-import-context@0.1.8(unrs-resolver@1.9.0):
    eslint-import-context: private
  eslint-merge-processors@2.0.0(eslint@9.29.0(jiti@2.4.2)):
    eslint-merge-processors: private
  eslint-plugin-import-x@4.15.2(@typescript-eslint/utils@8.34.0(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.29.0(jiti@2.4.2)):
    eslint-plugin-import-x: private
  eslint-plugin-jsdoc@50.8.0(eslint@9.29.0(jiti@2.4.2)):
    eslint-plugin-jsdoc: private
  eslint-plugin-regexp@2.9.0(eslint@9.29.0(jiti@2.4.2)):
    eslint-plugin-regexp: private
  eslint-plugin-unicorn@59.0.1(eslint@9.29.0(jiti@2.4.2)):
    eslint-plugin-unicorn: private
  eslint-plugin-vue@10.2.0(eslint@9.29.0(jiti@2.4.2))(vue-eslint-parser@10.1.3(eslint@9.29.0(jiti@2.4.2))):
    eslint-plugin-vue: private
  eslint-processor-vue-blocks@2.0.0(@vue/compiler-sfc@3.5.16)(eslint@9.29.0(jiti@2.4.2)):
    eslint-processor-vue-blocks: private
  eslint-scope@8.4.0:
    eslint-scope: private
  eslint-typegen@2.2.0(eslint@9.29.0(jiti@2.4.2)):
    eslint-typegen: private
  eslint-visitor-keys@4.2.1:
    eslint-visitor-keys: private
  espree@10.4.0:
    espree: private
  esprima@4.0.1:
    esprima: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@3.0.3:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  event-target-shim@5.0.1:
    event-target-shim: private
  events@3.3.0:
    events: private
  execa@8.0.1:
    execa: private
  expand-template@2.0.3:
    expand-template: private
  exsolve@1.0.5:
    exsolve: private
  externality@1.0.2:
    externality: private
  extract-zip@2.0.1:
    extract-zip: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-fifo@1.3.2:
    fast-fifo: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-npm-meta@0.4.3:
    fast-npm-meta: private
  fastq@1.19.1:
    fastq: private
  fd-slicer@1.1.0:
    fd-slicer: private
  fdir@6.4.6(picomatch@4.0.2):
    fdir: private
  fecha@4.2.3:
    fecha: private
  fetch-blob@3.2.0:
    fetch-blob: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  file-uri-to-path@1.0.0:
    file-uri-to-path: private
  fill-range@7.1.1:
    fill-range: private
  filter-obj@6.1.0:
    filter-obj: private
  find-up-simple@1.0.1:
    find-up-simple: private
  find-up@7.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  fn.name@1.1.0:
    fn.name: private
  fontaine@0.6.0:
    fontaine: private
  fontkit@2.0.4:
    fontkit: private
  foreground-child@3.3.1:
    foreground-child: private
  formdata-polyfill@4.0.10:
    formdata-polyfill: private
  fraction.js@4.3.7:
    fraction.js: private
  fresh@2.0.0:
    fresh: private
  fs-constants@1.0.0:
    fs-constants: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  fuse.js@7.1.0:
    fuse.js: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-amd-module-type@6.0.1:
    get-amd-module-type: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-port-please@3.1.2:
    get-port-please: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@8.0.1:
    get-stream: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  giget@2.0.0:
    giget: private
  git-up@8.1.1:
    git-up: private
  git-url-parse@16.1.0:
    git-url-parse: private
  github-from-package@0.0.0:
    github-from-package: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@10.4.5:
    glob: private
  global-directory@4.0.1:
    global-directory: private
  globals@14.0.0:
    globals: private
  globby@14.1.0:
    globby: private
  gonzales-pe@4.3.0:
    gonzales-pe: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  gzip-size@7.0.0:
    gzip-size: private
  h3@1.15.3:
    h3: private
  has-flag@4.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  hasown@2.0.2:
    hasown: private
  hookable@5.5.3:
    hookable: private
  hosted-git-info@7.0.2:
    hosted-git-info: private
  http-errors@2.0.0:
    http-errors: private
  http-shutdown@1.2.2:
    http-shutdown: private
  https-proxy-agent@7.0.6:
    https-proxy-agent: private
  httpxy@0.1.7:
    httpxy: private
  human-signals@5.0.0:
    human-signals: private
  ieee754@1.2.1:
    ieee754: private
  ignore@5.3.2:
    ignore: private
  image-meta@0.2.1:
    image-meta: private
  import-fresh@3.3.1:
    import-fresh: private
  impound@1.0.0:
    impound: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@5.0.0:
    indent-string: private
  index-to-position@1.1.0:
    index-to-position: private
  inherits@2.0.4:
    inherits: private
  ini@4.1.1:
    ini: private
  ioredis@5.6.1:
    ioredis: private
  ipx@2.1.0(db0@0.3.2)(ioredis@5.6.1):
    ipx: private
  iron-webcrypto@1.2.1:
    iron-webcrypto: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-builtin-module@5.0.0:
    is-builtin-module: private
  is-core-module@2.16.1:
    is-core-module: private
  is-docker@3.0.0:
    is-docker: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-inside-container@1.0.0:
    is-inside-container: private
  is-installed-globally@1.0.0:
    is-installed-globally: private
  is-module@1.0.0:
    is-module: private
  is-number@7.0.0:
    is-number: private
  is-path-inside@4.0.0:
    is-path-inside: private
  is-plain-obj@2.1.0:
    is-plain-obj: private
  is-reference@1.2.1:
    is-reference: private
  is-ssh@1.4.1:
    is-ssh: private
  is-stream@4.0.1:
    is-stream: private
  is-url-superb@4.0.0:
    is-url-superb: private
  is-url@1.2.4:
    is-url: private
  is-what@4.1.16:
    is-what: private
  is-wsl@3.1.0:
    is-wsl: private
  is64bit@2.0.0:
    is64bit: private
  isarray@1.0.0:
    isarray: private
  isexe@2.0.0:
    isexe: private
  jackspeak@3.4.3:
    jackspeak: private
  jiti@2.4.2:
    jiti: private
  js-tokens@9.0.1:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsdoc-type-pratt-parser@4.1.0:
    jsdoc-type-pratt-parser: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-to-typescript-lite@14.1.0:
    json-schema-to-typescript-lite: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@2.2.3:
    json5: private
  jsonc-eslint-parser@2.4.0:
    jsonc-eslint-parser: private
  junk@4.0.1:
    junk: private
  jwt-decode@4.0.0:
    jwt-decode: private
  keyv@4.5.4:
    keyv: private
  kleur@4.1.5:
    kleur: private
  klona@2.0.6:
    klona: private
  knitwork@1.2.0:
    knitwork: private
  kolorist@1.8.0:
    kolorist: private
  kuler@2.0.0:
    kuler: private
  lambda-local@2.2.0:
    lambda-local: private
  launch-editor@2.10.0:
    launch-editor: private
  lazystream@1.0.1:
    lazystream: private
  levn@0.4.1:
    levn: private
  lightningcss-darwin-arm64@1.30.1:
    lightningcss-darwin-arm64: private
  lightningcss-darwin-x64@1.30.1:
    lightningcss-darwin-x64: private
  lightningcss-freebsd-x64@1.30.1:
    lightningcss-freebsd-x64: private
  lightningcss-linux-arm-gnueabihf@1.30.1:
    lightningcss-linux-arm-gnueabihf: private
  lightningcss-linux-arm64-gnu@1.30.1:
    lightningcss-linux-arm64-gnu: private
  lightningcss-linux-arm64-musl@1.30.1:
    lightningcss-linux-arm64-musl: private
  lightningcss-linux-x64-gnu@1.30.1:
    lightningcss-linux-x64-gnu: private
  lightningcss-linux-x64-musl@1.30.1:
    lightningcss-linux-x64-musl: private
  lightningcss-win32-arm64-msvc@1.30.1:
    lightningcss-win32-arm64-msvc: private
  lightningcss-win32-x64-msvc@1.30.1:
    lightningcss-win32-x64-msvc: private
  lightningcss@1.30.1:
    lightningcss: private
  lilconfig@3.1.3:
    lilconfig: private
  listhen@1.9.0:
    listhen: private
  load-tsconfig@0.2.5:
    load-tsconfig: private
  local-pkg@1.1.1:
    local-pkg: private
  locate-path@6.0.0:
    locate-path: private
  lodash-es@4.17.21:
    lodash-es: private
  lodash.debounce@4.0.8:
    lodash.debounce: private
  lodash.defaults@4.2.0:
    lodash.defaults: private
  lodash.isarguments@3.1.0:
    lodash.isarguments: private
  lodash.memoize@4.1.2:
    lodash.memoize: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.uniq@4.5.0:
    lodash.uniq: private
  lodash@4.17.21:
    lodash: private
  logform@2.7.0:
    logform: private
  lru-cache@10.4.3:
    lru-cache: private
  luxon@3.6.1:
    luxon: private
  magic-regexp@0.10.0:
    magic-regexp: private
  magic-string-ast@0.7.1:
    magic-string-ast: private
  magic-string@0.30.17:
    magic-string: private
  magicast@0.3.5:
    magicast: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mdn-data@2.12.2:
    mdn-data: private
  merge-options@3.0.4:
    merge-options: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  micro-api-client@3.3.0:
    micro-api-client: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.54.0:
    mime-db: private
  mime-types@3.0.1:
    mime-types: private
  mime@4.0.7:
    mime: private
  mimic-fn@4.0.0:
    mimic-fn: private
  mimic-response@3.1.0:
    mimic-response: private
  min-indent@1.0.1:
    min-indent: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  minizlib@3.0.2:
    minizlib: private
  mitt@3.0.1:
    mitt: private
  mkdirp-classic@0.5.3:
    mkdirp-classic: private
  mkdirp@3.0.1:
    mkdirp: private
  mlly@1.7.4:
    mlly: private
  mocked-exports@0.1.1:
    mocked-exports: private
  module-definition@6.0.1:
    module-definition: private
  mrmime@2.0.1:
    mrmime: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  nanotar@0.2.0:
    nanotar: private
  napi-build-utils@2.0.0:
    napi-build-utils: private
  napi-postinstall@0.2.4:
    napi-postinstall: private
  natural-compare@1.4.0:
    natural-compare: private
  netlify@13.3.5:
    netlify: private
  nitropack@2.11.12:
    nitropack: private
  node-abi@3.75.0:
    node-abi: private
  node-addon-api@7.1.1:
    node-addon-api: private
  node-domexception@1.0.0:
    node-domexception: private
  node-fetch-native@1.6.6:
    node-fetch-native: private
  node-fetch@2.7.0:
    node-fetch: private
  node-forge@1.3.1:
    node-forge: private
  node-gyp-build@4.8.4:
    node-gyp-build: private
  node-mock-http@1.0.0:
    node-mock-http: private
  node-releases@2.0.19:
    node-releases: private
  node-source-walk@7.0.1:
    node-source-walk: private
  nopt@8.1.0:
    nopt: private
  normalize-package-data@6.0.2:
    normalize-package-data: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  npm-run-path@5.3.0:
    npm-run-path: private
  nth-check@2.1.1:
    nth-check: private
  nypm@0.6.0:
    nypm: private
  object-inspect@1.13.4:
    object-inspect: private
  ofetch@1.4.1:
    ofetch: private
  ohash@2.0.11:
    ohash: private
  on-change@5.0.1:
    on-change: private
  on-finished@2.4.1:
    on-finished: private
  once@1.4.0:
    once: private
  one-time@1.0.0:
    one-time: private
  onetime@6.0.0:
    onetime: private
  open@10.1.2:
    open: private
  optionator@0.9.4:
    optionator: private
  oxc-parser@0.70.0:
    oxc-parser: private
  p-event@6.0.1:
    p-event: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-map@7.0.3:
    p-map: private
  p-timeout@6.1.4:
    p-timeout: private
  p-wait-for@5.0.2:
    p-wait-for: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  package-manager-detector@1.3.0:
    package-manager-detector: private
  pako@0.2.9:
    pako: private
  parent-module@1.0.1:
    parent-module: private
  parse-gitignore@2.0.0:
    parse-gitignore: private
  parse-imports-exports@0.2.4:
    parse-imports-exports: private
  parse-json@8.3.0:
    parse-json: private
  parse-path@7.1.0:
    parse-path: private
  parse-statements@1.0.11:
    parse-statements: private
  parse-url@9.2.0:
    parse-url: private
  parseurl@1.3.3:
    parseurl: private
  path-exists@4.0.0:
    path-exists: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  path-type@6.0.0:
    path-type: private
  pathe@2.0.3:
    pathe: private
  pend@1.2.0:
    pend: private
  perfect-debounce@1.0.0:
    perfect-debounce: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  pkg-types@2.1.0:
    pkg-types: private
  pluralize@8.0.0:
    pluralize: private
  postcss-calc@10.1.1(postcss@8.5.5):
    postcss-calc: private
  postcss-colormin@7.0.3(postcss@8.5.5):
    postcss-colormin: private
  postcss-convert-values@7.0.5(postcss@8.5.5):
    postcss-convert-values: private
  postcss-discard-comments@7.0.4(postcss@8.5.5):
    postcss-discard-comments: private
  postcss-discard-duplicates@7.0.2(postcss@8.5.5):
    postcss-discard-duplicates: private
  postcss-discard-empty@7.0.1(postcss@8.5.5):
    postcss-discard-empty: private
  postcss-discard-overridden@7.0.1(postcss@8.5.5):
    postcss-discard-overridden: private
  postcss-merge-longhand@7.0.5(postcss@8.5.5):
    postcss-merge-longhand: private
  postcss-merge-rules@7.0.5(postcss@8.5.5):
    postcss-merge-rules: private
  postcss-minify-font-values@7.0.1(postcss@8.5.5):
    postcss-minify-font-values: private
  postcss-minify-gradients@7.0.1(postcss@8.5.5):
    postcss-minify-gradients: private
  postcss-minify-params@7.0.3(postcss@8.5.5):
    postcss-minify-params: private
  postcss-minify-selectors@7.0.5(postcss@8.5.5):
    postcss-minify-selectors: private
  postcss-normalize-charset@7.0.1(postcss@8.5.5):
    postcss-normalize-charset: private
  postcss-normalize-display-values@7.0.1(postcss@8.5.5):
    postcss-normalize-display-values: private
  postcss-normalize-positions@7.0.1(postcss@8.5.5):
    postcss-normalize-positions: private
  postcss-normalize-repeat-style@7.0.1(postcss@8.5.5):
    postcss-normalize-repeat-style: private
  postcss-normalize-string@7.0.1(postcss@8.5.5):
    postcss-normalize-string: private
  postcss-normalize-timing-functions@7.0.1(postcss@8.5.5):
    postcss-normalize-timing-functions: private
  postcss-normalize-unicode@7.0.3(postcss@8.5.5):
    postcss-normalize-unicode: private
  postcss-normalize-url@7.0.1(postcss@8.5.5):
    postcss-normalize-url: private
  postcss-normalize-whitespace@7.0.1(postcss@8.5.5):
    postcss-normalize-whitespace: private
  postcss-ordered-values@7.0.2(postcss@8.5.5):
    postcss-ordered-values: private
  postcss-reduce-initial@7.0.3(postcss@8.5.5):
    postcss-reduce-initial: private
  postcss-reduce-transforms@7.0.1(postcss@8.5.5):
    postcss-reduce-transforms: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss-svgo@7.0.2(postcss@8.5.5):
    postcss-svgo: private
  postcss-unique-selectors@7.0.4(postcss@8.5.5):
    postcss-unique-selectors: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  postcss-values-parser@6.0.2(postcss@8.5.5):
    postcss-values-parser: private
  postcss@8.5.5:
    postcss: private
  prebuild-install@7.1.3:
    prebuild-install: private
  precinct@12.2.0:
    precinct: private
  prelude-ls@1.2.1:
    prelude-ls: private
  pretty-bytes@6.1.1:
    pretty-bytes: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  process@0.11.10:
    process: private
  prompts@2.4.2:
    prompts: private
  protocols@2.0.2:
    protocols: private
  pump@3.0.3:
    pump: private
  punycode@2.3.1:
    punycode: private
  qs@6.14.0:
    qs: private
  quansync@0.2.10:
    quansync: private
  queue-microtask@1.2.3:
    queue-microtask: private
  quote-unquote@1.0.0:
    quote-unquote: private
  radix3@1.1.2:
    radix3: private
  randombytes@2.1.0:
    randombytes: private
  range-parser@1.2.1:
    range-parser: private
  rc9@2.1.2:
    rc9: private
  rc@1.2.8:
    rc: private
  read-package-up@11.0.0:
    read-package-up: private
  read-pkg@9.0.1:
    read-pkg: private
  readable-stream@4.7.0:
    readable-stream: private
  readdir-glob@1.1.3:
    readdir-glob: private
  readdirp@4.1.2:
    readdirp: private
  redis-errors@1.2.0:
    redis-errors: private
  redis-parser@3.0.0:
    redis-parser: private
  refa@0.12.1:
    refa: private
  regexp-ast-analysis@0.7.1:
    regexp-ast-analysis: private
  regexp-tree@0.1.27:
    regexp-tree: private
  regjsparser@0.12.0:
    regjsparser: private
  reka-ui@2.3.1(typescript@5.8.3)(vue@3.5.16(typescript@5.8.3)):
    reka-ui: private
  remove-trailing-separator@1.1.0:
    remove-trailing-separator: private
  require-directory@2.1.1:
    require-directory: private
  require-package-name@2.0.1:
    require-package-name: private
  resolve-from@5.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve@1.22.10:
    resolve: private
  restructure@3.0.2:
    restructure: private
  reusify@1.1.0:
    reusify: private
  rfdc@1.4.1:
    rfdc: private
  rollup-plugin-visualizer@6.0.3(rollup@4.43.0):
    rollup-plugin-visualizer: private
  rollup@4.43.0:
    rollup: private
  run-applescript@7.0.0:
    run-applescript: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-stable-stringify@2.5.0:
    safe-stable-stringify: private
  scslre@0.3.0:
    scslre: private
  scule@1.3.0:
    scule: private
  semver@7.7.2:
    semver: private
  send@1.2.0:
    send: private
  serialize-javascript@6.0.2:
    serialize-javascript: private
  serve-placeholder@2.0.2:
    serve-placeholder: private
  serve-static@2.2.0:
    serve-static: private
  setprototypeof@1.2.0:
    setprototypeof: private
  sharp@0.32.6:
    sharp: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shell-quote@1.8.3:
    shell-quote: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@4.1.0:
    signal-exit: private
  simple-concat@1.0.1:
    simple-concat: private
  simple-get@4.0.1:
    simple-get: private
  simple-git@3.28.0:
    simple-git: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  sirv@3.0.1:
    sirv: private
  sisteransi@1.0.5:
    sisteransi: private
  slash@5.1.0:
    slash: private
  smob@1.5.0:
    smob: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.7.4:
    source-map: private
  spdx-correct@3.2.0:
    spdx-correct: private
  spdx-exceptions@2.5.0:
    spdx-exceptions: private
  spdx-expression-parse@4.0.0:
    spdx-expression-parse: private
  spdx-license-ids@3.0.21:
    spdx-license-ids: private
  speakingurl@14.0.1:
    speakingurl: private
  stable-hash-x@0.1.1:
    stable-hash-x: private
  stack-trace@0.0.10:
    stack-trace: private
  standard-as-callback@2.1.0:
    standard-as-callback: private
  statuses@2.0.2:
    statuses: private
  std-env@3.9.0:
    std-env: private
  streamx@2.22.1:
    streamx: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  string_decoder@1.3.0:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi-cjs: private
  strip-ansi@7.1.0:
    strip-ansi: private
  strip-final-newline@3.0.0:
    strip-final-newline: private
  strip-indent@4.0.0:
    strip-indent: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  strip-literal@3.0.0:
    strip-literal: private
  structured-clone-es@1.0.0:
    structured-clone-es: private
  stylehacks@7.0.5(postcss@8.5.5):
    stylehacks: private
  superjson@2.2.2:
    superjson: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  svgo@3.3.2:
    svgo: private
  system-architecture@0.1.0:
    system-architecture: private
  tailwind-merge@3.0.2:
    tailwind-merge: private
  tailwind-variants@1.0.0(tailwindcss@4.1.10):
    tailwind-variants: private
  tapable@2.2.2:
    tapable: private
  tar-fs@3.0.9:
    tar-fs: private
  tar-stream@3.1.7:
    tar-stream: private
  tar@7.4.3:
    tar: private
  terser@5.42.0:
    terser: private
  text-decoder@1.2.3:
    text-decoder: private
  text-hex@1.0.0:
    text-hex: private
  tiny-inflate@1.0.3:
    tiny-inflate: private
  tiny-invariant@1.3.3:
    tiny-invariant: private
  tinyexec@1.0.1:
    tinyexec: private
  tinyglobby@0.2.14:
    tinyglobby: private
  tmp-promise@3.0.3:
    tmp-promise: private
  tmp@0.2.3:
    tmp: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  toml@3.0.0:
    toml: private
  tosource@2.0.0-alpha.3:
    tosource: private
  totalist@3.0.1:
    totalist: private
  tr46@0.0.3:
    tr46: private
  triple-beam@1.4.1:
    triple-beam: private
  ts-api-utils@2.1.0(typescript@5.8.3):
    ts-api-utils: private
  tslib@2.8.1:
    tslib: private
  tunnel-agent@0.6.0:
    tunnel-agent: private
  type-check@0.4.0:
    type-check: private
  type-fest@4.41.0:
    type-fest: private
  type-level-regexp@0.1.17:
    type-level-regexp: private
  ufo@1.6.1:
    ufo: private
  ultrahtml@1.6.0:
    ultrahtml: private
  uncrypto@0.1.3:
    uncrypto: private
  unctx@2.4.1:
    unctx: private
  undici-types@7.8.0:
    undici-types: private
  unenv@2.0.0-rc.17:
    unenv: private
  unhead@2.0.10:
    unhead: private
  unicode-properties@1.4.1:
    unicode-properties: private
  unicode-trie@2.0.0:
    unicode-trie: private
  unicorn-magic@0.1.0:
    unicorn-magic: private
  unifont@0.4.1:
    unifont: private
  unimport@5.0.1:
    unimport: private
  unixify@1.0.0:
    unixify: private
  unplugin-auto-import@19.3.0(@nuxt/kit@3.17.5(magicast@0.3.5))(@vueuse/core@13.3.0(vue@3.5.16(typescript@5.8.3))):
    unplugin-auto-import: private
  unplugin-utils@0.2.4:
    unplugin-utils: private
  unplugin-vue-components@28.7.0(@babel/parser@7.27.5)(@nuxt/kit@3.17.5(magicast@0.3.5))(vue@3.5.16(typescript@5.8.3)):
    unplugin-vue-components: private
  unplugin-vue-router@0.12.0(vue-router@4.5.1(vue@3.5.16(typescript@5.8.3)))(vue@3.5.16(typescript@5.8.3)):
    unplugin-vue-router: private
  unplugin@2.3.5:
    unplugin: private
  unrs-resolver@1.9.0:
    unrs-resolver: private
  unstorage@1.16.0(db0@0.3.2)(ioredis@5.6.1):
    unstorage: private
  untun@0.1.3:
    untun: private
  untyped@2.0.0:
    untyped: private
  unwasm@0.3.9:
    unwasm: private
  update-browserslist-db@1.1.3(browserslist@4.25.0):
    update-browserslist-db: private
  uqr@0.1.2:
    uqr: private
  uri-js@4.4.1:
    uri-js: private
  urlpattern-polyfill@8.0.2:
    urlpattern-polyfill: private
  util-deprecate@1.0.2:
    util-deprecate: private
  uuid@11.1.0:
    uuid: private
  validate-npm-package-license@3.0.4:
    validate-npm-package-license: private
  vaul-vue@0.4.1(reka-ui@2.3.1(typescript@5.8.3)(vue@3.5.16(typescript@5.8.3)))(vue@3.5.16(typescript@5.8.3)):
    vaul-vue: private
  vite-dev-rpc@1.0.7(vite@6.3.5(@types/node@24.0.2)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.42.0)(yaml@2.8.0)):
    vite-dev-rpc: private
  vite-hot-client@2.0.4(vite@6.3.5(@types/node@24.0.2)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.42.0)(yaml@2.8.0)):
    vite-hot-client: private
  vite-node@3.2.3(@types/node@24.0.2)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.42.0)(yaml@2.8.0):
    vite-node: private
  vite-plugin-checker@0.9.3(eslint@9.29.0(jiti@2.4.2))(optionator@0.9.4)(typescript@5.8.3)(vite@6.3.5(@types/node@24.0.2)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.42.0)(yaml@2.8.0)):
    vite-plugin-checker: private
  vite-plugin-inspect@11.2.0(@nuxt/kit@3.17.5(magicast@0.3.5))(vite@6.3.5(@types/node@24.0.2)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.42.0)(yaml@2.8.0)):
    vite-plugin-inspect: private
  vite-plugin-vue-tracer@0.1.4(vite@6.3.5(@types/node@24.0.2)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.42.0)(yaml@2.8.0))(vue@3.5.16(typescript@5.8.3)):
    vite-plugin-vue-tracer: private
  vite@6.3.5(@types/node@24.0.2)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.42.0)(yaml@2.8.0):
    vite: private
  vscode-uri@3.1.0:
    vscode-uri: private
  vue-bundle-renderer@2.1.1:
    vue-bundle-renderer: private
  vue-component-type-helpers@2.2.10:
    vue-component-type-helpers: private
  vue-demi@0.14.10(vue@3.5.16(typescript@5.8.3)):
    vue-demi: private
  vue-devtools-stub@0.1.0:
    vue-devtools-stub: private
  vue-eslint-parser@10.1.3(eslint@9.29.0(jiti@2.4.2)):
    vue-eslint-parser: private
  vue-i18n@10.0.7(vue@3.5.16(typescript@5.8.3)):
    vue-i18n: private
  web-streams-polyfill@3.3.3:
    web-streams-polyfill: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  webpack-virtual-modules@0.6.2:
    webpack-virtual-modules: private
  whatwg-url@5.0.0:
    whatwg-url: private
  wheel-gestures@2.2.48:
    wheel-gestures: private
  which@5.0.0:
    which: private
  winston-transport@4.9.0:
    winston-transport: private
  winston@3.17.0:
    winston: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
    wrap-ansi-cjs: private
  wrappy@1.0.2:
    wrappy: private
  write-file-atomic@6.0.0:
    write-file-atomic: private
  ws@8.18.2:
    ws: private
  xml-name-validator@4.0.0:
    xml-name-validator: private
  xss@1.0.15:
    xss: private
  y18n@5.0.8:
    y18n: private
  yallist@5.0.0:
    yallist: private
  yaml-eslint-parser@1.3.0:
    yaml-eslint-parser: private
  yaml@2.8.0:
    yaml: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yauzl@2.10.0:
    yauzl: private
  yocto-queue@0.1.0:
    yocto-queue: private
  youch-core@0.3.2:
    youch-core: private
  youch@4.1.0-beta.8:
    youch: private
  zip-stream@6.0.1:
    zip-stream: private
  zod@3.25.64:
    zod: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.5.2
pendingBuilds: []
prunedAt: Mon, 16 Jun 2025 09:52:09 GMT
publicHoistPattern: []
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/core@1.4.3'
  - '@emnapi/runtime@1.4.3'
  - '@emnapi/wasi-threads@1.0.2'
  - '@esbuild/aix-ppc64@0.25.5'
  - '@esbuild/android-arm64@0.25.5'
  - '@esbuild/android-arm@0.25.5'
  - '@esbuild/android-x64@0.25.5'
  - '@esbuild/darwin-arm64@0.25.5'
  - '@esbuild/darwin-x64@0.25.5'
  - '@esbuild/freebsd-arm64@0.25.5'
  - '@esbuild/freebsd-x64@0.25.5'
  - '@esbuild/linux-arm64@0.25.5'
  - '@esbuild/linux-arm@0.25.5'
  - '@esbuild/linux-ia32@0.25.5'
  - '@esbuild/linux-loong64@0.25.5'
  - '@esbuild/linux-mips64el@0.25.5'
  - '@esbuild/linux-ppc64@0.25.5'
  - '@esbuild/linux-riscv64@0.25.5'
  - '@esbuild/linux-s390x@0.25.5'
  - '@esbuild/linux-x64@0.25.5'
  - '@esbuild/netbsd-arm64@0.25.5'
  - '@esbuild/netbsd-x64@0.25.5'
  - '@esbuild/openbsd-arm64@0.25.5'
  - '@esbuild/openbsd-x64@0.25.5'
  - '@esbuild/sunos-x64@0.25.5'
  - '@esbuild/win32-arm64@0.25.5'
  - '@esbuild/win32-ia32@0.25.5'
  - '@napi-rs/wasm-runtime@0.2.11'
  - '@oxc-parser/binding-darwin-arm64@0.70.0'
  - '@oxc-parser/binding-darwin-arm64@0.72.3'
  - '@oxc-parser/binding-darwin-x64@0.70.0'
  - '@oxc-parser/binding-darwin-x64@0.72.3'
  - '@oxc-parser/binding-freebsd-x64@0.70.0'
  - '@oxc-parser/binding-freebsd-x64@0.72.3'
  - '@oxc-parser/binding-linux-arm-gnueabihf@0.70.0'
  - '@oxc-parser/binding-linux-arm-gnueabihf@0.72.3'
  - '@oxc-parser/binding-linux-arm-musleabihf@0.70.0'
  - '@oxc-parser/binding-linux-arm-musleabihf@0.72.3'
  - '@oxc-parser/binding-linux-arm64-gnu@0.70.0'
  - '@oxc-parser/binding-linux-arm64-gnu@0.72.3'
  - '@oxc-parser/binding-linux-arm64-musl@0.70.0'
  - '@oxc-parser/binding-linux-arm64-musl@0.72.3'
  - '@oxc-parser/binding-linux-riscv64-gnu@0.70.0'
  - '@oxc-parser/binding-linux-riscv64-gnu@0.72.3'
  - '@oxc-parser/binding-linux-s390x-gnu@0.70.0'
  - '@oxc-parser/binding-linux-s390x-gnu@0.72.3'
  - '@oxc-parser/binding-linux-x64-gnu@0.70.0'
  - '@oxc-parser/binding-linux-x64-gnu@0.72.3'
  - '@oxc-parser/binding-linux-x64-musl@0.70.0'
  - '@oxc-parser/binding-linux-x64-musl@0.72.3'
  - '@oxc-parser/binding-wasm32-wasi@0.70.0'
  - '@oxc-parser/binding-wasm32-wasi@0.72.3'
  - '@oxc-parser/binding-win32-arm64-msvc@0.70.0'
  - '@oxc-parser/binding-win32-arm64-msvc@0.72.3'
  - '@parcel/watcher-android-arm64@2.5.1'
  - '@parcel/watcher-darwin-arm64@2.5.1'
  - '@parcel/watcher-darwin-x64@2.5.1'
  - '@parcel/watcher-freebsd-x64@2.5.1'
  - '@parcel/watcher-linux-arm-glibc@2.5.1'
  - '@parcel/watcher-linux-arm-musl@2.5.1'
  - '@parcel/watcher-linux-arm64-glibc@2.5.1'
  - '@parcel/watcher-linux-arm64-musl@2.5.1'
  - '@parcel/watcher-linux-x64-glibc@2.5.1'
  - '@parcel/watcher-linux-x64-musl@2.5.1'
  - '@parcel/watcher-win32-arm64@2.5.1'
  - '@parcel/watcher-win32-ia32@2.5.1'
  - '@rollup/rollup-android-arm-eabi@4.43.0'
  - '@rollup/rollup-android-arm64@4.43.0'
  - '@rollup/rollup-darwin-arm64@4.43.0'
  - '@rollup/rollup-darwin-x64@4.43.0'
  - '@rollup/rollup-freebsd-arm64@4.43.0'
  - '@rollup/rollup-freebsd-x64@4.43.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.43.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.43.0'
  - '@rollup/rollup-linux-arm64-gnu@4.43.0'
  - '@rollup/rollup-linux-arm64-musl@4.43.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.43.0'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.43.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.43.0'
  - '@rollup/rollup-linux-riscv64-musl@4.43.0'
  - '@rollup/rollup-linux-s390x-gnu@4.43.0'
  - '@rollup/rollup-linux-x64-gnu@4.43.0'
  - '@rollup/rollup-linux-x64-musl@4.43.0'
  - '@rollup/rollup-win32-arm64-msvc@4.43.0'
  - '@rollup/rollup-win32-ia32-msvc@4.43.0'
  - '@tailwindcss/oxide-android-arm64@4.1.10'
  - '@tailwindcss/oxide-darwin-arm64@4.1.10'
  - '@tailwindcss/oxide-darwin-x64@4.1.10'
  - '@tailwindcss/oxide-freebsd-x64@4.1.10'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.10'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.10'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.10'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.10'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.10'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.10'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.10'
  - '@tybys/wasm-util@0.9.0'
  - '@unrs/resolver-binding-android-arm-eabi@1.9.0'
  - '@unrs/resolver-binding-android-arm64@1.9.0'
  - '@unrs/resolver-binding-darwin-arm64@1.9.0'
  - '@unrs/resolver-binding-darwin-x64@1.9.0'
  - '@unrs/resolver-binding-freebsd-x64@1.9.0'
  - '@unrs/resolver-binding-linux-arm-gnueabihf@1.9.0'
  - '@unrs/resolver-binding-linux-arm-musleabihf@1.9.0'
  - '@unrs/resolver-binding-linux-arm64-gnu@1.9.0'
  - '@unrs/resolver-binding-linux-arm64-musl@1.9.0'
  - '@unrs/resolver-binding-linux-ppc64-gnu@1.9.0'
  - '@unrs/resolver-binding-linux-riscv64-gnu@1.9.0'
  - '@unrs/resolver-binding-linux-riscv64-musl@1.9.0'
  - '@unrs/resolver-binding-linux-s390x-gnu@1.9.0'
  - '@unrs/resolver-binding-linux-x64-gnu@1.9.0'
  - '@unrs/resolver-binding-linux-x64-musl@1.9.0'
  - '@unrs/resolver-binding-wasm32-wasi@1.9.0'
  - '@unrs/resolver-binding-win32-arm64-msvc@1.9.0'
  - '@unrs/resolver-binding-win32-ia32-msvc@1.9.0'
  - fsevents@2.3.3
  - lightningcss-darwin-arm64@1.30.1
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\OneDrive\Töölaud\ecommerce\client\node_modules\.pnpm
virtualStoreDirMaxLength: 60
