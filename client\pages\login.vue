<template>
  <div
    class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"
  >
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Sign in to your account
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          Or
          <NuxtLink
            to="/register"
            class="font-medium text-blue-600 hover:text-blue-500"
          >
            create a new account
          </NuxtLink>
        </p>
      </div>

      <form class="mt-8 space-y-6" @submit.prevent="handleLogin">
        <div class="space-y-4">
          <div>
            <label
              for="email"
              class="block text-sm font-medium text-gray-700 mb-2"
              >Email address</label
            >
            <UInput
              id="email"
              v-model="form.email"
              type="email"
              placeholder="Enter your email address"
              required
              class="w-full"
              :disabled="isLoading"
            />
          </div>
          <div>
            <label
              for="password"
              class="block text-sm font-medium text-gray-700 mb-2"
              >Password</label
            >
            <UInput
              id="password"
              v-model="form.password"
              type="password"
              placeholder="Enter your password"
              required
              class="w-full"
              :disabled="isLoading"
            />
          </div>
        </div>

        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <input
              id="remember-me"
              v-model="form.rememberMe"
              type="checkbox"
              :disabled="isLoading"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label for="remember-me" class="ml-2 block text-sm text-gray-900">
              Remember me
            </label>
          </div>

          <div class="text-sm">
            <NuxtLink
              to="/forgot-password"
              class="font-medium text-blue-600 hover:text-blue-500"
            >
              Forgot your password?
            </NuxtLink>
          </div>
        </div>

        <div>
          <UButton
            type="submit"
            :loading="isLoading"
            :disabled="!form.email || !form.password"
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Sign in
          </UButton>
        </div>

        <div v-if="error" class="text-red-600 text-sm text-center">
          {{ error }}
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
const authStore = useAuthStore();
const router = useRouter();

// Form data
const form = ref({
  email: "",
  password: "",
  rememberMe: false,
});

const isLoading = ref(false);
const error = ref("");

// Handle login
const handleLogin = async () => {
  if (!form.value.email || !form.value.password) {
    error.value = "Please fill in all fields";
    return;
  }

  isLoading.value = true;
  error.value = "";

  try {
    await authStore.login(form.value.email, form.value.password);

    // Redirect based on user role
    const redirect = router.currentRoute.value.query.redirect;
    if (redirect) {
      await router.push(redirect);
    } else if (authStore.isAdmin) {
      await router.push("/admin");
    } else {
      await router.push("/");
    }
  } catch (err) {
    error.value = err.message || "Login failed. Please try again.";
  } finally {
    isLoading.value = false;
  }
};

// SEO
useHead({
  title: "Login - Ecommerce Store",
  meta: [
    {
      name: "description",
      content:
        "Sign in to your account to access your orders, wishlist, and account settings.",
    },
  ],
});
</script>
