const express = require('express');
const { body, validationResult } = require('express-validator');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

const router = express.Router();
const prisma = new PrismaClient();

// Create order and payment intent
router.post('/create', authenticateToken, [
  body('shippingAddress').isObject(),
  body('billingAddress').optional().isObject(),
  body('discountCode').optional().trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { shippingAddress, billingAddress, discountCode } = req.body;

    // Get cart items
    const cartItems = await prisma.cartItem.findMany({
      where: { userId: req.user.id },
      include: {
        product: true,
        variant: true
      }
    });

    if (cartItems.length === 0) {
      return res.status(400).json({ error: 'Cart is empty' });
    }

    // Calculate totals
    let subtotal = 0;
    const orderItems = [];

    for (const item of cartItems) {
      const price = item.variant?.price || item.product.price;
      const total = parseFloat(price) * item.quantity;
      subtotal += total;

      // Check stock availability
      const availableStock = item.variant?.stock || item.product.stock;
      if (item.quantity > availableStock) {
        return res.status(400).json({ 
          error: `Insufficient stock for ${item.product.name}`,
          product: item.product.name,
          requested: item.quantity,
          available: availableStock
        });
      }

      orderItems.push({
        productId: item.productId,
        variantId: item.variantId,
        quantity: item.quantity,
        price: parseFloat(price),
        total
      });
    }

    // Apply discount if provided
    let discount = 0;
    if (discountCode) {
      if (discountCode === 'WELCOME15') {
        // Check if user hasn't used this discount before
        const subscriber = await prisma.emailSubscriber.findUnique({
          where: { email: req.user.email }
        });
        
        if (subscriber && !subscriber.discountUsed) {
          discount = subtotal * 0.15; // 15% discount
        }
      }
    }

    const tax = subtotal * 0.1; // 10% tax
    const shipping = subtotal > 100 ? 0 : 10; // Free shipping over $100
    const total = subtotal + tax + shipping - discount;

    // Generate order number
    const orderNumber = 'ORD-' + Date.now() + '-' + Math.random().toString(36).substr(2, 5).toUpperCase();

    // Create Stripe payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(total * 100), // Convert to cents
      currency: 'usd',
      metadata: {
        userId: req.user.id,
        orderNumber
      }
    });

    // Create order
    const order = await prisma.order.create({
      data: {
        orderNumber,
        userId: req.user.id,
        subtotal,
        tax,
        shipping,
        discount,
        total,
        discountCode,
        paymentId: paymentIntent.id,
        shippingAddress,
        billingAddress: billingAddress || shippingAddress,
        items: {
          create: orderItems
        }
      },
      include: {
        items: {
          include: {
            product: true,
            variant: true
          }
        }
      }
    });

    res.json({
      order,
      clientSecret: paymentIntent.client_secret
    });
  } catch (error) {
    console.error('Create order error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Confirm order after successful payment
router.post('/:orderId/confirm', authenticateToken, async (req, res) => {
  try {
    const { orderId } = req.params;
    const { paymentIntentId } = req.body;

    // Verify payment with Stripe
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
    
    if (paymentIntent.status !== 'succeeded') {
      return res.status(400).json({ error: 'Payment not completed' });
    }

    // Update order
    const order = await prisma.order.update({
      where: { id: orderId },
      data: {
        status: 'CONFIRMED',
        paymentStatus: 'PAID'
      },
      include: {
        items: {
          include: {
            product: true,
            variant: true
          }
        }
      }
    });

    // Update stock and clear cart
    await prisma.$transaction(async (tx) => {
      // Update stock
      for (const item of order.items) {
        if (item.variantId) {
          await tx.productVariant.update({
            where: { id: item.variantId },
            data: { stock: { decrement: item.quantity } }
          });
        } else {
          await tx.product.update({
            where: { id: item.productId },
            data: { stock: { decrement: item.quantity } }
          });
        }
      }

      // Clear cart
      await tx.cartItem.deleteMany({
        where: { userId: req.user.id }
      });

      // Mark discount as used
      if (order.discountCode === 'WELCOME15') {
        await tx.emailSubscriber.updateMany({
          where: { email: req.user.email },
          data: { discountUsed: true }
        });
      }
    });

    res.json({
      message: 'Order confirmed successfully',
      order
    });
  } catch (error) {
    console.error('Confirm order error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get user's orders
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    const [orders, total] = await Promise.all([
      prisma.order.findMany({
        where: { userId: req.user.id },
        skip,
        take,
        orderBy: { createdAt: 'desc' },
        include: {
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                  images: { take: 1, select: { url: true, alt: true } }
                }
              },
              variant: {
                select: { id: true, name: true, value: true, type: true }
              }
            }
          }
        }
      }),
      prisma.order.count({ where: { userId: req.user.id } })
    ]);

    const totalPages = Math.ceil(total / take);

    res.json({
      orders,
      pagination: {
        page: parseInt(page),
        limit: take,
        total,
        totalPages
      }
    });
  } catch (error) {
    console.error('Get orders error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get single order
router.get('/:orderId', authenticateToken, async (req, res) => {
  try {
    const { orderId } = req.params;

    const order = await prisma.order.findFirst({
      where: { 
        id: orderId,
        userId: req.user.id 
      },
      include: {
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                slug: true,
                images: { take: 1, select: { url: true, alt: true } }
              }
            },
            variant: {
              select: { id: true, name: true, value: true, type: true }
            }
          }
        }
      }
    });

    if (!order) {
      return res.status(404).json({ error: 'Order not found' });
    }

    res.json({ order });
  } catch (error) {
    console.error('Get order error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
