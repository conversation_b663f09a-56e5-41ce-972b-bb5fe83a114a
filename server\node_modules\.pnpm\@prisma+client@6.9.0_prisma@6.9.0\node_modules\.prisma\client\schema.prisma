// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  firstName String?
  lastName  String?
  role      UserRole @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  orders Order[]
  cart   CartItem[]

  @@map("users")
}

model Category {
  id          String     @id @default(cuid())
  name        String
  slug        String     @unique
  description String?
  image       String?
  parentId    String?
  parent      Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children    Category[] @relation("CategoryHierarchy")
  isActive    Boolean    @default(true)
  sortOrder   Int        @default(0)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  products Product[]

  @@map("categories")
}

model Product {
  id           String   @id @default(cuid())
  name         String
  slug         String   @unique
  description  String?
  shortDesc    String?
  price        Decimal  @db.Decimal(10, 2)
  comparePrice Decimal? @db.Decimal(10, 2)
  sku          String?  @unique
  stock        Int      @default(0)
  isActive     Boolean  @default(true)
  isFeatured   Boolean  @default(false)
  weight       Decimal? @db.Decimal(8, 2)
  dimensions   String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  categoryId String
  category   Category @relation(fields: [categoryId], references: [id])

  images     ProductImage[]
  variants   ProductVariant[]
  cartItems  CartItem[]
  orderItems OrderItem[]

  @@map("products")
}

model ProductImage {
  id        String  @id @default(cuid())
  url       String
  alt       String?
  sortOrder Int     @default(0)
  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_images")
}

model ProductVariant {
  id        String      @id @default(cuid())
  name      String
  value     String
  type      VariantType
  stock     Int         @default(0)
  price     Decimal?    @db.Decimal(10, 2)
  sku       String?
  productId String
  product   Product     @relation(fields: [productId], references: [id], onDelete: Cascade)

  cartItems  CartItem[]
  orderItems OrderItem[]

  @@map("product_variants")
}

model CartItem {
  id        String   @id @default(cuid())
  quantity  Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  userId    String
  user      User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  productId String
  product   Product         @relation(fields: [productId], references: [id], onDelete: Cascade)
  variantId String?
  variant   ProductVariant? @relation(fields: [variantId], references: [id], onDelete: Cascade)

  @@unique([userId, productId, variantId])
  @@map("cart_items")
}

model Order {
  id            String        @id @default(cuid())
  orderNumber   String        @unique
  status        OrderStatus   @default(PENDING)
  total         Decimal       @db.Decimal(10, 2)
  subtotal      Decimal       @db.Decimal(10, 2)
  tax           Decimal       @db.Decimal(10, 2)
  shipping      Decimal       @db.Decimal(10, 2)
  discount      Decimal?      @db.Decimal(10, 2)
  discountCode  String?
  paymentStatus PaymentStatus @default(PENDING)
  paymentId     String?
  notes         String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  userId String
  user   User   @relation(fields: [userId], references: [id])

  shippingAddress Json
  billingAddress  Json?
  items           OrderItem[]

  @@map("orders")
}

model OrderItem {
  id       String  @id @default(cuid())
  quantity Int
  price    Decimal @db.Decimal(10, 2)
  total    Decimal @db.Decimal(10, 2)

  orderId   String
  order     Order           @relation(fields: [orderId], references: [id], onDelete: Cascade)
  productId String
  product   Product         @relation(fields: [productId], references: [id])
  variantId String?
  variant   ProductVariant? @relation(fields: [variantId], references: [id])

  @@map("order_items")
}

model EmailSubscriber {
  id           String   @id @default(cuid())
  email        String   @unique
  isActive     Boolean  @default(true)
  discountUsed Boolean  @default(false)
  source       String? // popup, footer, etc.
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@map("email_subscribers")
}

model SiteSettings {
  id        String   @id @default(cuid())
  key       String   @unique
  value     Json
  updatedAt DateTime @updatedAt

  @@map("site_settings")
}

model Slideshow {
  id         String   @id @default(cuid())
  title      String?
  subtitle   String?
  content    String?
  image      String
  link       String?
  buttonText String?
  isActive   Boolean  @default(true)
  sortOrder  Int      @default(0)
  styles     Json? // For custom styling
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@map("slideshows")
}

enum UserRole {
  USER
  ADMIN
}

enum VariantType {
  COLOR
  SIZE
  MATERIAL
  STYLE
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
}
