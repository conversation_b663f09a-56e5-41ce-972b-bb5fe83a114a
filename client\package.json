{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/eslint": "1.4.1", "@nuxt/fonts": "0.11.4", "@nuxt/icon": "1.13.0", "@nuxt/image": "1.10.0", "@nuxt/ui": "3.1.3", "@nuxtjs/i18n": "^9.5.5", "@nuxtjs/tailwindcss": "7.0.0-beta.0", "@pinia/nuxt": "^0.11.1", "@stripe/stripe-js": "^7.3.1", "@vueuse/nuxt": "^13.3.0", "autoprefixer": "^10.4.0", "eslint": "^9.0.0", "nuxt": "^3.17.5", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.3.0", "tailwindcss": "^4.1.10", "typescript": "^5.6.3", "vue": "^3.5.16", "vue-router": "^4.5.1"}}