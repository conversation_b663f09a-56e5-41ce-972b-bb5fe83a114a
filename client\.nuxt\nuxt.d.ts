/// <reference types="@nuxt/devtools" />
/// <reference types="@nuxt/devtools" />
/// <reference types="@nuxt/telemetry" />
/// <reference types="@nuxt/icon" />
/// <reference types="@nuxt/image" />
/// <reference types="@vueuse/nuxt" />
/// <reference types="@nuxt/eslint" />
/// <reference types="@nuxtjs/tailwindcss" />
/// <reference types="@nuxt/ui" />
/// <reference types="@pinia/nuxt" />
/// <reference types="@nuxtjs/i18n" />
/// <reference types="@nuxt/fonts" />
/// <reference path="types/builder-env.d.ts" />
/// <reference types="nuxt" />
/// <reference path="types/app-defaults.d.ts" />
/// <reference path="types/plugins.d.ts" />
/// <reference path="types/build.d.ts" />
/// <reference path="types/schema.d.ts" />
/// <reference path="types/app.config.d.ts" />
/// <reference path="types/ui.d.ts" />
/// <reference path="../node_modules/.pnpm/@nuxt+ui@3.1.3_@babel+parse_7a9c4136c496b19031bb5938edf59a72/node_modules/@nuxt/ui/dist/runtime/types/app.config.d.ts" />
/// <reference types="@pinia/nuxt" />
/// <reference types="vue-router" />
/// <reference path="types/middleware.d.ts" />
/// <reference path="types/nitro-middleware.d.ts" />
/// <reference path="types/layouts.d.ts" />
/// <reference path="components.d.ts" />
/// <reference path="imports.d.ts" />
/// <reference path="types/imports.d.ts" />
/// <reference path="schema/nuxt.schema.d.ts" />
/// <reference path="types/i18n-plugin.d.ts" />
/// <reference path="types/nitro.d.ts" />
/// <reference path="./eslint-typegen.d.ts" />

export {}
