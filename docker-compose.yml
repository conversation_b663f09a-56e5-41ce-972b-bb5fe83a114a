version: '3.8'

services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

  server:
    build:
      context: ./server
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://root:Onamission%23007@************:5432/ecommerce_db
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-super-secret-jwt-key-change-in-production
      - STRIPE_SECRET_KEY=your-stripe-secret-key
      - EMAIL_HOST=smtp.zoho.eu
      - EMAIL_PORT=587
      - EMAIL_SECURE=false
      - EMAIL_USER=<EMAIL>
      - EMAIL_PASS=Onamission#007
    depends_on:
      - redis
    volumes:
      - ./server/uploads:/app/uploads

  client:
    build:
      context: ./client
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NUXT_PUBLIC_API_BASE=http://localhost:3001
    depends_on:
      - server

volumes:
  redis_data:
