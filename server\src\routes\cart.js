const express = require('express');
const { body, validationResult } = require('express-validator');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Get user's cart
router.get('/', authenticateToken, async (req, res) => {
  try {
    const cartItems = await prisma.cartItem.findMany({
      where: { userId: req.user.id },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            slug: true,
            price: true,
            stock: true,
            isActive: true,
            images: {
              take: 1,
              select: { url: true, alt: true }
            }
          }
        },
        variant: {
          select: {
            id: true,
            name: true,
            value: true,
            type: true,
            stock: true,
            price: true
          }
        }
      }
    });

    // Calculate totals
    const subtotal = cartItems.reduce((total, item) => {
      const price = item.variant?.price || item.product.price;
      return total + (parseFloat(price) * item.quantity);
    }, 0);

    const itemCount = cartItems.reduce((count, item) => count + item.quantity, 0);

    res.json({
      items: cartItems,
      subtotal: subtotal.toFixed(2),
      itemCount
    });
  } catch (error) {
    console.error('Get cart error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Add item to cart
router.post('/add', authenticateToken, [
  body('productId').notEmpty(),
  body('quantity').isInt({ min: 1 }),
  body('variantId').optional()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { productId, quantity, variantId } = req.body;

    // Check if product exists and is active
    const product = await prisma.product.findFirst({
      where: { id: productId, isActive: true },
      include: {
        variants: variantId ? { where: { id: variantId } } : false
      }
    });

    if (!product) {
      return res.status(404).json({ error: 'Product not found' });
    }

    // Check stock
    const availableStock = variantId 
      ? product.variants[0]?.stock || 0
      : product.stock;

    if (quantity > availableStock) {
      return res.status(400).json({ 
        error: 'Insufficient stock',
        availableStock 
      });
    }

    // Check if item already exists in cart
    const existingItem = await prisma.cartItem.findUnique({
      where: {
        userId_productId_variantId: {
          userId: req.user.id,
          productId,
          variantId: variantId || null
        }
      }
    });

    let cartItem;

    if (existingItem) {
      // Update quantity
      const newQuantity = existingItem.quantity + quantity;
      
      if (newQuantity > availableStock) {
        return res.status(400).json({ 
          error: 'Insufficient stock',
          availableStock 
        });
      }

      cartItem = await prisma.cartItem.update({
        where: { id: existingItem.id },
        data: { quantity: newQuantity },
        include: {
          product: {
            select: {
              id: true,
              name: true,
              slug: true,
              price: true,
              images: { take: 1, select: { url: true, alt: true } }
            }
          },
          variant: {
            select: {
              id: true,
              name: true,
              value: true,
              type: true,
              price: true
            }
          }
        }
      });
    } else {
      // Create new cart item
      cartItem = await prisma.cartItem.create({
        data: {
          userId: req.user.id,
          productId,
          variantId,
          quantity
        },
        include: {
          product: {
            select: {
              id: true,
              name: true,
              slug: true,
              price: true,
              images: { take: 1, select: { url: true, alt: true } }
            }
          },
          variant: {
            select: {
              id: true,
              name: true,
              value: true,
              type: true,
              price: true
            }
          }
        }
      });
    }

    res.json({ 
      message: 'Item added to cart',
      item: cartItem 
    });
  } catch (error) {
    console.error('Add to cart error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update cart item quantity
router.put('/:itemId', authenticateToken, [
  body('quantity').isInt({ min: 0 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { itemId } = req.params;
    const { quantity } = req.body;

    // Find cart item
    const cartItem = await prisma.cartItem.findFirst({
      where: { 
        id: itemId,
        userId: req.user.id 
      },
      include: {
        product: true,
        variant: true
      }
    });

    if (!cartItem) {
      return res.status(404).json({ error: 'Cart item not found' });
    }

    if (quantity === 0) {
      // Remove item
      await prisma.cartItem.delete({
        where: { id: itemId }
      });
      return res.json({ message: 'Item removed from cart' });
    }

    // Check stock
    const availableStock = cartItem.variant?.stock || cartItem.product.stock;
    if (quantity > availableStock) {
      return res.status(400).json({ 
        error: 'Insufficient stock',
        availableStock 
      });
    }

    // Update quantity
    const updatedItem = await prisma.cartItem.update({
      where: { id: itemId },
      data: { quantity },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            slug: true,
            price: true,
            images: { take: 1, select: { url: true, alt: true } }
          }
        },
        variant: {
          select: {
            id: true,
            name: true,
            value: true,
            type: true,
            price: true
          }
        }
      }
    });

    res.json({ 
      message: 'Cart updated',
      item: updatedItem 
    });
  } catch (error) {
    console.error('Update cart error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Remove item from cart
router.delete('/:itemId', authenticateToken, async (req, res) => {
  try {
    const { itemId } = req.params;

    const cartItem = await prisma.cartItem.findFirst({
      where: { 
        id: itemId,
        userId: req.user.id 
      }
    });

    if (!cartItem) {
      return res.status(404).json({ error: 'Cart item not found' });
    }

    await prisma.cartItem.delete({
      where: { id: itemId }
    });

    res.json({ message: 'Item removed from cart' });
  } catch (error) {
    console.error('Remove from cart error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Clear cart
router.delete('/', authenticateToken, async (req, res) => {
  try {
    await prisma.cartItem.deleteMany({
      where: { userId: req.user.id }
    });

    res.json({ message: 'Cart cleared' });
  } catch (error) {
    console.error('Clear cart error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
