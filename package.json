{"name": "ecommerce-webstore", "version": "1.0.0", "description": "Beautiful ecommerce webstore with admin panel", "scripts": {"dev": "concurrently \"npm run dev:client\" \"npm run dev:server\"", "dev:client": "cd client && npm run dev", "dev:server": "cd server && npm run dev", "build": "npm run build:client && npm run build:server", "build:client": "cd client && npm run build", "build:server": "cd server && npm run build", "start": "concurrently \"npm run start:client\" \"npm run start:server\"", "start:client": "cd client && npm run start", "start:server": "cd server && npm run start", "setup": "npm run setup:client && npm run setup:server", "setup:client": "cd client && npm install", "setup:server": "cd server && npm install", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:build": "docker-compose build"}, "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["client", "server"]}