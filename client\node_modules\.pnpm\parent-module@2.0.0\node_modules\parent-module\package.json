{"name": "parent-module", "version": "2.0.0", "description": "Get the path of the parent module", "license": "MIT", "repository": "sindresorhus/parent-module", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["parent", "module", "package", "caller", "calling", "module", "path", "callsites", "callsite", "stacktrace", "stack", "trace", "function", "file"], "dependencies": {"callsites": "^3.1.0"}, "devDependencies": {"ava": "^1.4.1", "execa": "^1.0.0", "tsd": "^0.7.2", "xo": "^0.24.0"}}