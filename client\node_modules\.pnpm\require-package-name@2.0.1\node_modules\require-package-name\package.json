{"name": "require-package-name", "version": "2.0.1", "description": "gets the package name for a require statement", "main": "index.js", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/mattdesl"}, "dependencies": {}, "devDependencies": {"tape": "^4.0.0"}, "scripts": {"test": "node test.js"}, "keywords": ["package", "name", "regex", "split", "base", "basedir", "basepath", "path", "require", "requires", "npm", "module"], "repository": {"type": "git", "url": "git://github.com/mattdesl/require-package-name.git"}, "homepage": "https://github.com/mattdesl/require-package-name", "bugs": {"url": "https://github.com/mattdesl/require-package-name/issues"}}