<template>
  <div class="product-card group">
    <!-- Product Image -->
    <div class="relative overflow-hidden bg-gray-100 rounded-t-xl">
      <NuxtLink :to="`/products/${product.slug}`">
        <img
          v-if="product.images && product.images.length > 0"
          :src="product.images[0].url"
          :alt="product.images[0].alt || product.name"
          class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
        />
        <div
          v-else
          class="w-full h-64 flex items-center justify-center bg-gray-200"
        >
          <UIcon name="i-heroicons-photo" class="w-12 h-12 text-gray-400" />
        </div>
      </NuxtLink>

      <!-- Quick actions -->
      <div
        class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
      >
        <button
          @click="toggleWishlist"
          class="bg-white/90 hover:bg-white text-gray-700 p-2 rounded-full shadow-md transition-colors mb-2"
        >
          <UIcon
            :name="
              isWishlisted ? 'i-heroicons-heart-solid' : 'i-heroicons-heart'
            "
            class="w-4 h-4"
            :class="isWishlisted ? 'text-red-500' : ''"
          />
        </button>
      </div>

      <!-- Stock status -->
      <div
        v-if="!isInStock"
        class="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium"
      >
        Sold Out
      </div>
      <div
        v-else-if="product.stock <= 5"
        class="absolute top-4 left-4 bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-medium"
      >
        Only {{ product.stock }} left
      </div>

      <!-- Sale badge -->
      <div
        v-if="product.comparePrice && product.comparePrice > product.price"
        class="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium"
        :class="{ 'mt-10': !isInStock || product.stock <= 5 }"
      >
        Sale
      </div>
    </div>

    <!-- Product Info -->
    <div class="p-6">
      <!-- Category -->
      <p class="text-sm text-gray-500 mb-2">{{ product.category.name }}</p>

      <!-- Product Name -->
      <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2">
        <NuxtLink
          :to="`/products/${product.slug}`"
          class="hover:text-blue-600 transition-colors"
        >
          {{ product.name }}
        </NuxtLink>
      </h3>

      <!-- Description -->
      <p
        v-if="product.shortDesc"
        class="text-sm text-gray-600 mb-4 line-clamp-2"
      >
        {{ product.shortDesc }}
      </p>

      <!-- Variants (if any) -->
      <div v-if="product.variants && product.variants.length > 0" class="mb-4">
        <!-- Color variants -->
        <div
          v-if="colorVariants.length > 0"
          class="flex items-center space-x-2 mb-2"
        >
          <span class="text-sm text-gray-500">Colors:</span>
          <div class="flex space-x-1">
            <button
              v-for="variant in colorVariants.slice(0, 4)"
              :key="variant.id"
              @click="selectedVariant = variant.id"
              class="w-6 h-6 rounded-full border-2 transition-all"
              :class="
                selectedVariant === variant.id
                  ? 'border-gray-900 scale-110'
                  : 'border-gray-300'
              "
              :style="{ backgroundColor: variant.value.toLowerCase() }"
              :title="variant.value"
            />
            <span
              v-if="colorVariants.length > 4"
              class="text-xs text-gray-500 self-center"
            >
              +{{ colorVariants.length - 4 }}
            </span>
          </div>
        </div>

        <!-- Size variants -->
        <div v-if="sizeVariants.length > 0" class="flex items-center space-x-2">
          <span class="text-sm text-gray-500">Sizes:</span>
          <div class="flex space-x-1">
            <button
              v-for="variant in sizeVariants.slice(0, 4)"
              :key="variant.id"
              @click="selectedVariant = variant.id"
              class="px-2 py-1 text-xs border rounded transition-all"
              :class="
                selectedVariant === variant.id
                  ? 'border-gray-900 bg-gray-900 text-white'
                  : 'border-gray-300 hover:border-gray-400'
              "
            >
              {{ variant.value }}
            </button>
            <span
              v-if="sizeVariants.length > 4"
              class="text-xs text-gray-500 self-center"
            >
              +{{ sizeVariants.length - 4 }}
            </span>
          </div>
        </div>
      </div>

      <!-- Price -->
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-2">
          <span class="text-lg font-bold text-gray-900">
            ${{ currentPrice.toFixed(2) }}
          </span>
          <span
            v-if="product.comparePrice && product.comparePrice > product.price"
            class="text-sm text-gray-500 line-through"
          >
            ${{ product.comparePrice.toFixed(2) }}
          </span>
        </div>

        <!-- Discount percentage -->
        <span
          v-if="discountPercentage > 0"
          class="text-sm font-medium text-red-600"
        >
          -{{ discountPercentage }}%
        </span>
      </div>

      <!-- Add to Cart Button -->
      <button
        @click="addToCart"
        :disabled="!isInStock || cartStore.isLoading"
        class="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all disabled:bg-gray-300 disabled:cursor-not-allowed"
      >
        <span
          v-if="cartStore.isLoading"
          class="flex items-center justify-center"
        >
          <div class="loading-spinner mr-2" />
          Adding...
        </span>
        <span v-else-if="!isInStock"> Sold Out </span>
        <span v-else> Add to Cart </span>
      </button>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  product: {
    type: Object,
    required: true,
  },
});
const cartStore = useCartStore();

// Reactive data
const selectedVariant = ref(null);
const isWishlisted = ref(false);

// Computed properties
const colorVariants = computed(() => {
  return props.product.variants?.filter((v) => v.type === "COLOR") || [];
});

const sizeVariants = computed(() => {
  return props.product.variants?.filter((v) => v.type === "SIZE") || [];
});

const currentPrice = computed(() => {
  if (selectedVariant.value) {
    const variant = props.product.variants?.find(
      (v) => v.id === selectedVariant.value
    );
    return variant?.price || props.product.price;
  }
  return props.product.price;
});

const isInStock = computed(() => {
  if (selectedVariant.value) {
    const variant = props.product.variants?.find(
      (v) => v.id === selectedVariant.value
    );
    return (variant?.stock || 0) > 0;
  }
  return props.product.stock > 0;
});

const discountPercentage = computed(() => {
  if (
    props.product.comparePrice &&
    props.product.comparePrice > props.product.price
  ) {
    return Math.round(
      ((props.product.comparePrice - props.product.price) /
        props.product.comparePrice) *
        100
    );
  }
  return 0;
});

// Methods
const addToCart = async () => {
  try {
    await cartStore.addItem(
      props.product.id,
      1,
      selectedVariant.value || undefined
    );
  } catch (error) {
    // Error handled in store
  }
};

const toggleWishlist = () => {
  isWishlisted.value = !isWishlisted.value;
  // TODO: Implement wishlist functionality
};

// Initialize with first variant if available
onMounted(() => {
  if (props.product.variants && props.product.variants.length > 0) {
    selectedVariant.value = props.product.variants[0].id;
  }
});
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
