const express = require("express");
const { body, validationResult } = require("express-validator");
const { PrismaClient } = require("@prisma/client");
const { authenticateToken, requireAdmin } = require("../middleware/auth");

const router = express.Router();
const prisma = new PrismaClient();

// Apply admin middleware to all routes
router.use(authenticateToken, requireAdmin);

// Dashboard stats
router.get("/dashboard", async (req, res) => {
  try {
    const [
      totalProducts,
      totalOrders,
      totalUsers,
      totalRevenue,
      recentOrders,
      topProducts,
    ] = await Promise.all([
      prisma.product.count({ where: { isActive: true } }),
      prisma.order.count(),
      prisma.user.count({ where: { role: "USER" } }),
      prisma.order.aggregate({
        where: { paymentStatus: "PAID" },
        _sum: { total: true },
      }),
      prisma.order.findMany({
        take: 5,
        orderBy: { createdAt: "desc" },
        include: {
          user: { select: { email: true, firstName: true, lastName: true } },
        },
      }),
      prisma.orderItem.groupBy({
        by: ["productId"],
        _sum: { quantity: true },
        orderBy: { _sum: { quantity: "desc" } },
        take: 5,
      }),
    ]);

    // Get product details for top products
    const topProductIds = topProducts.map((item) => item.productId);
    const productDetails = await prisma.product.findMany({
      where: { id: { in: topProductIds } },
      select: { id: true, name: true, price: true },
    });

    const topProductsWithDetails = topProducts.map((item) => ({
      ...productDetails.find((p) => p.id === item.productId),
      totalSold: item._sum.quantity,
    }));

    res.json({
      stats: {
        totalProducts,
        totalOrders,
        totalUsers,
        totalRevenue: totalRevenue._sum.total || 0,
      },
      recentOrders,
      topProducts: topProductsWithDetails,
    });
  } catch (error) {
    console.error("Dashboard error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

// Products management
router.get("/products", async (req, res) => {
  try {
    const { page = 1, limit = 20, search, category } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    const where = {
      ...(search && {
        OR: [
          { name: { contains: search, mode: "insensitive" } },
          { description: { contains: search, mode: "insensitive" } },
        ],
      }),
      ...(category && { categoryId: category }),
    };

    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where,
        skip,
        take,
        orderBy: { createdAt: "desc" },
        include: {
          category: { select: { name: true } },
          images: { take: 1 },
          _count: { select: { variants: true } },
        },
      }),
      prisma.product.count({ where }),
    ]);

    res.json({
      products,
      pagination: {
        page: parseInt(page),
        limit: take,
        total,
        totalPages: Math.ceil(total / take),
      },
    });
  } catch (error) {
    console.error("Get products error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

router.post(
  "/products",
  [
    body("name").notEmpty().trim(),
    body("slug").notEmpty().trim(),
    body("price").isFloat({ min: 0 }),
    body("categoryId").notEmpty(),
    body("description").optional().trim(),
    body("shortDesc").optional().trim(),
    body("stock").isInt({ min: 0 }),
    body("sku").optional().trim(),
    body("images").optional().isArray(),
    body("variants").optional().isArray(),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { images = [], variants = [], ...productData } = req.body;

      const product = await prisma.product.create({
        data: {
          ...productData,
          images: {
            create: images.map((img, index) => ({
              url: img.url,
              alt: img.alt || productData.name,
              sortOrder: index,
            })),
          },
          variants: {
            create: variants.map((variant) => ({
              name: variant.name,
              value: variant.value,
              type: variant.type,
              stock: variant.stock || 0,
              price: variant.price || null,
              sku: variant.sku || null,
            })),
          },
        },
        include: {
          images: true,
          variants: true,
          category: true,
        },
      });

      res.status(201).json({ product });
    } catch (error) {
      console.error("Create product error:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

router.put(
  "/products/:id",
  [
    body("name").optional().notEmpty().trim(),
    body("price").optional().isFloat({ min: 0 }),
    body("stock").optional().isInt({ min: 0 }),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { id } = req.params;
      const { images, variants, ...updateData } = req.body;

      const product = await prisma.product.update({
        where: { id },
        data: updateData,
        include: {
          images: true,
          variants: true,
          category: true,
        },
      });

      res.json({ product });
    } catch (error) {
      console.error("Update product error:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

router.delete("/products/:id", async (req, res) => {
  try {
    const { id } = req.params;

    await prisma.product.delete({
      where: { id },
    });

    res.json({ message: "Product deleted successfully" });
  } catch (error) {
    console.error("Delete product error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

// Categories management
router.get("/categories", async (req, res) => {
  try {
    const categories = await prisma.category.findMany({
      orderBy: { sortOrder: "asc" },
      include: {
        parent: { select: { name: true } },
        _count: { select: { products: true } },
      },
    });

    res.json({ categories });
  } catch (error) {
    console.error("Get categories error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

router.post(
  "/categories",
  [
    body("name").notEmpty().trim(),
    body("slug").notEmpty().trim(),
    body("description").optional().trim(),
    body("parentId").optional(),
    body("image").optional().trim(),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const category = await prisma.category.create({
        data: req.body,
        include: {
          parent: { select: { name: true } },
        },
      });

      res.status(201).json({ category });
    } catch (error) {
      console.error("Create category error:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

// Orders management
router.get("/orders", async (req, res) => {
  try {
    const { page = 1, limit = 20, status, search } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    const where = {
      ...(status && { status }),
      ...(search && {
        OR: [
          { orderNumber: { contains: search, mode: "insensitive" } },
          { user: { email: { contains: search, mode: "insensitive" } } },
        ],
      }),
    };

    const [orders, total] = await Promise.all([
      prisma.order.findMany({
        where,
        skip,
        take,
        orderBy: { createdAt: "desc" },
        include: {
          user: { select: { email: true, firstName: true, lastName: true } },
          items: {
            include: {
              product: { select: { name: true } },
            },
          },
        },
      }),
      prisma.order.count({ where }),
    ]);

    res.json({
      orders,
      pagination: {
        page: parseInt(page),
        limit: take,
        total,
        totalPages: Math.ceil(total / take),
      },
    });
  } catch (error) {
    console.error("Get orders error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

router.put(
  "/orders/:id/status",
  [
    body("status").isIn([
      "PENDING",
      "CONFIRMED",
      "PROCESSING",
      "SHIPPED",
      "DELIVERED",
      "CANCELLED",
      "REFUNDED",
    ]),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { id } = req.params;
      const { status } = req.body;

      const order = await prisma.order.update({
        where: { id },
        data: { status },
        include: {
          user: { select: { email: true, firstName: true, lastName: true } },
        },
      });

      res.json({ order });
    } catch (error) {
      console.error("Update order status error:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

// Slideshow management
router.get("/slideshows", async (req, res) => {
  try {
    const slideshows = await prisma.slideshow.findMany({
      orderBy: { sortOrder: "asc" },
    });

    res.json({ slideshows });
  } catch (error) {
    console.error("Get slideshows error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

router.post(
  "/slideshows",
  [
    body("title").optional().trim(),
    body("subtitle").optional().trim(),
    body("content").optional().trim(),
    body("image").notEmpty().trim(),
    body("link").optional().trim(),
    body("buttonText").optional().trim(),
    body("styles").optional().isObject(),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const slideshow = await prisma.slideshow.create({
        data: req.body,
      });

      res.status(201).json({ slideshow });
    } catch (error) {
      console.error("Create slideshow error:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

router.put("/slideshows/:id", async (req, res) => {
  try {
    const { id } = req.params;

    const slideshow = await prisma.slideshow.update({
      where: { id },
      data: req.body,
    });

    res.json({ slideshow });
  } catch (error) {
    console.error("Update slideshow error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

router.delete("/slideshows/:id", async (req, res) => {
  try {
    const { id } = req.params;

    await prisma.slideshow.delete({
      where: { id },
    });

    res.json({ message: "Slideshow deleted successfully" });
  } catch (error) {
    console.error("Delete slideshow error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

// Site settings management
router.get("/settings", async (req, res) => {
  try {
    const settings = await prisma.siteSettings.findMany();

    const settingsObj = {};
    settings.forEach((setting) => {
      settingsObj[setting.key] = setting.value;
    });

    res.json({ settings: settingsObj });
  } catch (error) {
    console.error("Get settings error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

router.put("/settings", async (req, res) => {
  try {
    const { settings } = req.body;

    const updatePromises = Object.entries(settings).map(([key, value]) =>
      prisma.siteSettings.upsert({
        where: { key },
        update: { value },
        create: { key, value },
      })
    );

    await Promise.all(updatePromises);

    res.json({ message: "Settings updated successfully" });
  } catch (error) {
    console.error("Update settings error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

module.exports = router;
