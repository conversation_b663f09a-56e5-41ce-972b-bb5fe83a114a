<template>
  <div>
    <!-- <PERSON>er -->
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">Products</h1>
        <p class="mt-2 text-gray-600">Manage your store's products</p>
      </div>
      <UButton to="/admin/products/new" icon="i-heroicons-plus">
        Add Product
      </UButton>
    </div>

    <!-- Filters and Search -->
    <UCard class="mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <UInput
          v-model="filters.search"
          placeholder="Search products..."
          icon="i-heroicons-magnifying-glass"
        />

        <USelect
          v-model="filters.category"
          :options="categoryOptions"
          placeholder="All Categories"
        />

        <USelect
          v-model="filters.status"
          :options="statusOptions"
          placeholder="All Status"
        />

        <UButton @click="applyFilters" block> Apply Filters </UButton>
      </div>
    </UCard>

    <!-- Products Table -->
    <UCard>
      <UTable
        :rows="products"
        :columns="columns"
        :loading="isLoading"
        :empty-state="{ icon: 'i-heroicons-cube', label: 'No products found' }"
      >
        <template #image-data="{ row }">
          <img
            v-if="row.images?.[0]?.url"
            :src="row.images[0].url"
            :alt="row.name"
            class="h-12 w-12 object-cover rounded-lg"
          />
          <div
            v-else
            class="h-12 w-12 bg-gray-200 rounded-lg flex items-center justify-center"
          >
            <UIcon name="i-heroicons-photo" class="h-6 w-6 text-gray-400" />
          </div>
        </template>

        <template #name-data="{ row }">
          <div>
            <p class="font-medium text-gray-900">{{ row.name }}</p>
            <p class="text-sm text-gray-500">{{ row.sku }}</p>
          </div>
        </template>

        <template #price-data="{ row }">
          <div>
            <p class="font-medium text-gray-900">${{ row.price }}</p>
            <p
              v-if="row.comparePrice"
              class="text-sm text-gray-500 line-through"
            >
              ${{ row.comparePrice }}
            </p>
          </div>
        </template>

        <template #stock-data="{ row }">
          <UBadge
            :color="row.stock > 10 ? 'green' : row.stock > 0 ? 'yellow' : 'red'"
            variant="soft"
          >
            {{ row.stock }} in stock
          </UBadge>
        </template>

        <template #status-data="{ row }">
          <UBadge :color="row.isActive ? 'green' : 'red'" variant="soft">
            {{ row.isActive ? "Active" : "Inactive" }}
          </UBadge>
        </template>

        <template #actions-data="{ row }">
          <UDropdown :items="getActionItems(row)">
            <UButton variant="ghost" icon="i-heroicons-ellipsis-horizontal" />
          </UDropdown>
        </template>
      </UTable>

      <!-- Pagination -->
      <div class="flex justify-between items-center mt-6">
        <p class="text-sm text-gray-500">
          Showing {{ (currentPage - 1) * pageSize + 1 }} to
          {{ Math.min(currentPage * pageSize, totalProducts) }} of
          {{ totalProducts }} products
        </p>

        <UPagination
          v-model="currentPage"
          :page-count="pageSize"
          :total="totalProducts"
          @update:model-value="fetchProducts"
        />
      </div>
    </UCard>
  </div>
</template>

<script setup>
definePageMeta({
  layout: "admin",
});

// Reactive data
const products = ref([]);
const isLoading = ref(true);
const currentPage = ref(1);
const pageSize = ref(10);
const totalProducts = ref(0);

const filters = ref({
  search: "",
  category: "",
  status: "",
});

// Table columns
const columns = [
  { key: "image", label: "Image" },
  { key: "name", label: "Product" },
  { key: "price", label: "Price" },
  { key: "stock", label: "Stock" },
  { key: "status", label: "Status" },
  { key: "actions", label: "Actions" },
];

// Filter options
const categoryOptions = ref([{ label: "All Categories", value: "" }]);

const statusOptions = [
  { label: "All Status", value: "" },
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
];

// Action items for dropdown
const getActionItems = (product) => [
  [
    {
      label: "Edit",
      icon: "i-heroicons-pencil",
      click: () => navigateTo(`/admin/products/${product.id}/edit`),
    },
  ],
  [
    {
      label: "Delete",
      icon: "i-heroicons-trash",
      click: () => deleteProduct(product.id),
    },
  ],
];

// Fetch products
const fetchProducts = async () => {
  try {
    isLoading.value = true;
    const { $api } = useNuxtApp();

    const params = new URLSearchParams({
      page: currentPage.value,
      limit: pageSize.value,
      ...filters.value,
    });

    const response = await $api(`/admin/products?${params}`);
    products.value = response.products || [];
    totalProducts.value = response.total || 0;
  } catch (error) {
    console.error("Failed to fetch products:", error);
  } finally {
    isLoading.value = false;
  }
};

// Apply filters
const applyFilters = () => {
  currentPage.value = 1;
  fetchProducts();
};

// Delete product
const deleteProduct = async (productId) => {
  if (!confirm("Are you sure you want to delete this product?")) return;

  try {
    const { $api } = useNuxtApp();
    await $api(`/admin/products/${productId}`, { method: "DELETE" });
    await fetchProducts();
  } catch (error) {
    console.error("Failed to delete product:", error);
  }
};

// Fetch categories for filter
const fetchCategories = async () => {
  try {
    const { $api } = useNuxtApp();
    const response = await $api("/admin/categories");
    const categories = response.categories || [];

    categoryOptions.value = [
      { label: "All Categories", value: "" },
      ...categories.map((cat) => ({ label: cat.name, value: cat.id })),
    ];
  } catch (error) {
    console.error("Failed to fetch categories:", error);
  }
};

// Load data on mount
onMounted(() => {
  fetchProducts();
  fetchCategories();
});

// SEO
useHead({
  title: "Products - Admin Panel",
});
</script>
