const express = require('express');
const { PrismaClient } = require('@prisma/client');

const router = express.Router();
const prisma = new PrismaClient();

// Get all categories with hierarchy
router.get('/', async (req, res) => {
  try {
    const categories = await prisma.category.findMany({
      where: { isActive: true },
      orderBy: { sortOrder: 'asc' },
      include: {
        children: {
          where: { isActive: true },
          orderBy: { sortOrder: 'asc' },
          include: {
            _count: {
              select: { products: { where: { isActive: true } } }
            }
          }
        },
        _count: {
          select: { products: { where: { isActive: true } } }
        }
      }
    });

    // Filter to only parent categories (no parentId)
    const parentCategories = categories.filter(cat => !cat.parentId);

    res.json({ categories: parentCategories });
  } catch (error) {
    console.error('Get categories error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get single category by slug
router.get('/:slug', async (req, res) => {
  try {
    const { slug } = req.params;

    const category = await prisma.category.findUnique({
      where: { slug, isActive: true },
      include: {
        children: {
          where: { isActive: true },
          orderBy: { sortOrder: 'asc' }
        },
        parent: {
          select: { id: true, name: true, slug: true }
        },
        _count: {
          select: { products: { where: { isActive: true } } }
        }
      }
    });

    if (!category) {
      return res.status(404).json({ error: 'Category not found' });
    }

    res.json({ category });
  } catch (error) {
    console.error('Get category error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get category tree for navigation
router.get('/tree/navigation', async (req, res) => {
  try {
    const categories = await prisma.category.findMany({
      where: { 
        isActive: true,
        parentId: null // Only root categories
      },
      orderBy: { sortOrder: 'asc' },
      select: {
        id: true,
        name: true,
        slug: true,
        image: true,
        children: {
          where: { isActive: true },
          orderBy: { sortOrder: 'asc' },
          select: {
            id: true,
            name: true,
            slug: true,
            image: true
          }
        }
      }
    });

    res.json({ categories });
  } catch (error) {
    console.error('Get category tree error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
