{"name": "@csstools/selector-resolve-nested", "description": "Resolve nested CSS selectors", "version": "1.1.0", "contributors": [{"name": "Antonio <PERSON>", "email": "<EMAIL>", "url": "https://antonio.laguna.es"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT-0", "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "engines": {"node": "^14 || ^16 || >=18"}, "type": "module", "main": "dist/index.cjs", "module": "dist/index.mjs", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist"], "peerDependencies": {"postcss-selector-parser": "^6.0.13"}, "devDependencies": {"postcss-selector-parser": "^6.0.13"}, "scripts": {"build": "rollup -c ../../rollup/default.mjs", "docs": "node ../../.github/bin/generate-docs/api-documenter.mjs", "lint": "node ../../.github/bin/format-package-json.mjs", "prepublishOnly": "npm run build && npm run test", "stryker": "stryker run --logLevel error", "test": "node --test ./test/index.mjs ./test/_import.mjs && node ./test/_require.cjs"}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/selector-resolve-nested#readme", "repository": {"type": "git", "url": "https://github.com/csstools/postcss-plugins.git", "directory": "packages/selector-resolve-nested"}, "bugs": "https://github.com/csstools/postcss-plugins/issues", "keywords": ["css", "nested", "postcss-selector-parser"], "volta": {"extends": "../../package.json"}}