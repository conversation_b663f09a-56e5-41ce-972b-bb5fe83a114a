/* Custom styles for the ecommerce store */

/* Base styles */
html {
  scroll-behavior: smooth;
}

body {
  font-family: "Inter", sans-serif;
  line-height: 1.6;
}

/* Custom spacing utilities */
.container-padding {
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container-padding {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-padding {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.section-padding {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

@media (min-width: 640px) {
  .section-padding {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }
}

@media (min-width: 1024px) {
  .section-padding {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }
}

.card-spacing {
  padding: 1.5rem;
}

@media (min-width: 640px) {
  .card-spacing {
    padding: 2rem;
  }
}

.card-gap {
  gap: 1.5rem;
}

@media (min-width: 640px) {
  .card-gap {
    gap: 2rem;
  }
}

/* Card styles with proper separation */
.product-card {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border: 1px solid #f3f4f6;
  transition: all 0.3s ease;
  overflow: hidden;
}

.product-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-0.25rem);
}

.category-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border: 1px solid #f3f4f6;
  transition: all 0.3s ease;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.category-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Slideshow styles */
.slideshow-container {
  position: relative;
  overflow: hidden;
  border-radius: 1rem;
  height: 80vh;
}

.slide-content {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
  background: linear-gradient(45deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.2));
}

.slide-title {
  font-size: 2.25rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

@media (min-width: 640px) {
  .slide-title {
    font-size: 3rem;
  }
}

@media (min-width: 1024px) {
  .slide-title {
    font-size: 3.75rem;
  }
}

.slide-subtitle {
  font-size: 1.125rem;
  margin-bottom: 1.5rem;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

@media (min-width: 640px) {
  .slide-subtitle {
    font-size: 1.25rem;
  }
}

@media (min-width: 1024px) {
  .slide-subtitle {
    font-size: 1.5rem;
  }
}

/* Header styles */
.header-bar {
  @apply bg-white shadow-sm border-b border-gray-100 sticky top-0 z-50;
}

.logo-container {
  @apply flex items-center space-x-3;
}

/* Cart drawer styles */
.cart-drawer {
  @apply fixed right-0 top-0 h-full w-full max-w-md bg-white shadow-2xl z-50 transform transition-transform duration-300;
}

.cart-drawer.open {
  @apply translate-x-0;
}

.cart-drawer.closed {
  @apply translate-x-full;
}

/* Product grid styles */
.product-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 sm:gap-8;
}

/* Category sidebar styles */
.category-sidebar {
  @apply bg-white rounded-lg shadow-sm border border-gray-100 p-6 sticky top-24;
}

.category-item {
  @apply block py-3 px-4 rounded-lg hover:bg-gray-50 transition-colors duration-200 border-b border-gray-100 last:border-b-0;
}

.category-item.active {
  @apply bg-blue-50 text-blue-600 font-medium;
}

/* Form styles */
.form-input {
  @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200;
}

.form-button {
  @apply px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 font-medium;
}

/* Modal styles */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4;
}

.modal-content {
  @apply bg-white rounded-xl shadow-2xl max-w-md w-full p-6 transform transition-all duration-300;
}

/* Notification styles */
.notification-bar {
  @apply bg-gradient-to-r from-blue-600 to-purple-600 text-white py-2 px-4 text-center text-sm font-medium;
}

/* Loading states */
.loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600;
}

/* Responsive utilities */
@media (max-width: 640px) {
  .slide-title {
    @apply text-3xl;
  }

  .slide-subtitle {
    @apply text-base;
  }

  .product-grid {
    @apply grid-cols-1 gap-4;
  }
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Animation utilities */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
