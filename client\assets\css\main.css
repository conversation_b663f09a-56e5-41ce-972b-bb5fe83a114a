@import "tailwindcss";
@import "@nuxt/ui";

/* Custom styles for the ecommerce store */

/* Base styles */
html {
  scroll-behavior: smooth;
}

body {
  font-family: "Inter", sans-serif;
  line-height: 1.6;
}

/* Custom utility classes */
.container-padding {
  @apply px-4 sm:px-6 lg:px-8;
}

.section-padding {
  @apply py-8 sm:py-12 lg:py-16;
}

.card-spacing {
  padding: 1.5rem;
}

@media (min-width: 640px) {
  .card-spacing {
    padding: 2rem;
  }
}

.card-gap {
  gap: 1.5rem;
}

@media (min-width: 640px) {
  .card-gap {
    gap: 2rem;
  }
}

/* Card styles with proper separation */
.product-card {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border: 1px solid #f3f4f6;
  transition: all 0.3s ease;
  overflow: hidden;
}

.product-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-0.25rem);
}

.category-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border: 1px solid #f3f4f6;
  transition: all 0.3s ease;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.category-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Slideshow styles */
.slideshow-container {
  position: relative;
  overflow: hidden;
  border-radius: 1rem;
  height: 80vh;
}

.slide-content {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
  background: linear-gradient(45deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.2));
}

.slide-title {
  font-size: 2.25rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

@media (min-width: 640px) {
  .slide-title {
    font-size: 3rem;
  }
}

@media (min-width: 1024px) {
  .slide-title {
    font-size: 3.75rem;
  }
}

.slide-subtitle {
  font-size: 1.125rem;
  margin-bottom: 1.5rem;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

@media (min-width: 640px) {
  .slide-subtitle {
    font-size: 1.25rem;
  }
}

@media (min-width: 1024px) {
  .slide-subtitle {
    font-size: 1.5rem;
  }
}

/* Header styles */
.header-bar {
  background-color: white;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border-bottom: 1px solid #f3f4f6;
  position: sticky;
  top: 0;
  z-index: 50;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Cart drawer styles */
.cart-drawer {
  position: fixed;
  right: 0;
  top: 0;
  height: 100%;
  width: 100%;
  max-width: 28rem;
  background-color: white;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  z-index: 50;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.cart-drawer.open {
  transform: translateX(0);
}

.cart-drawer.closed {
  transform: translateX(100%);
}

/* Product grid styles */
.product-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 640px) {
  .product-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
}

@media (min-width: 1024px) {
  .product-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .product-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Category sidebar styles */
.category-sidebar {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border: 1px solid #f3f4f6;
  padding: 1.5rem;
  position: sticky;
  top: 6rem;
}

.category-item {
  display: block;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f3f4f6;
}

.category-item:last-child {
  border-bottom: none;
}

.category-item:hover {
  background-color: #f9fafb;
}

.category-item.active {
  background-color: #eff6ff;
  color: #2563eb;
  font-weight: 500;
}

/* Form styles */
.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: transparent;
  box-shadow: 0 0 0 2px #3b82f6;
}

.form-button {
  padding: 0.75rem 1.5rem;
  background-color: #2563eb;
  color: white;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
  font-weight: 500;
  border: none;
  cursor: pointer;
}

.form-button:hover {
  background-color: #1d4ed8;
}

.form-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px #3b82f6, 0 0 0 4px rgba(59, 130, 246, 0.1);
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.modal-content {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  max-width: 28rem;
  width: 100%;
  padding: 1.5rem;
  transform: scale(1);
  transition: all 0.3s ease;
}

/* Notification styles */
.notification-bar {
  background: linear-gradient(to right, #2563eb, #7c3aed);
  color: white;
  padding: 0.5rem 1rem;
  text-align: center;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Loading states */
.loading-spinner {
  animation: spin 1s linear infinite;
  border-radius: 50%;
  height: 2rem;
  width: 2rem;
  border-bottom: 2px solid #2563eb;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive utilities */
@media (max-width: 640px) {
  .slide-title {
    font-size: 1.875rem;
  }

  .slide-subtitle {
    font-size: 1rem;
  }

  .product-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Animation utilities */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
