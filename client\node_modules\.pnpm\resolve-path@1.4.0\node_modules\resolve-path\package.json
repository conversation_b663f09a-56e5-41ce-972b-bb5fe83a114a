{"name": "resolve-path", "description": "Resolve a relative path against a root path with validation", "version": "1.4.0", "author": "<PERSON> <<EMAIL>> (http://jongleberry.com)", "contributors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "repository": "pillarjs/resolve-path", "dependencies": {"http-errors": "~1.6.2", "path-is-absolute": "1.0.1"}, "devDependencies": {"eslint": "3.19.0", "eslint-config-standard": "10.2.1", "eslint-plugin-import": "2.8.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-node": "5.2.1", "eslint-plugin-promise": "3.6.0", "eslint-plugin-standard": "3.0.1", "istanbul": "0.4.5", "mocha": "2.5.3"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "engines": {"node": ">= 0.8"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "keywords": ["resolve", "path", "safe"]}