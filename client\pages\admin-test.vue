<template>
  <div class="min-h-screen bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
      <div class="bg-white rounded-lg shadow p-6">
        <h1 class="text-3xl font-bold text-gray-900 mb-6">Admin Test Page</h1>
        
        <div class="space-y-4">
          <div class="p-4 bg-blue-50 rounded-lg">
            <h2 class="text-lg font-semibold text-blue-900 mb-2">Authentication Status</h2>
            <p><strong>Authenticated:</strong> {{ authStore.isAuthenticated ? 'Yes' : 'No' }}</p>
            <p><strong>User:</strong> {{ authStore.user ? authStore.fullName : 'None' }}</p>
            <p><strong>Role:</strong> {{ authStore.user?.role || 'None' }}</p>
            <p><strong>Is Admin:</strong> {{ authStore.isAdmin ? 'Yes' : 'No' }}</p>
          </div>

          <div class="p-4 bg-green-50 rounded-lg">
            <h2 class="text-lg font-semibold text-green-900 mb-2">Quick Actions</h2>
            <div class="space-x-4">
              <button
                v-if="!authStore.isAuthenticated"
                @click="quickLogin"
                class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
              >
                Quick Admin Login
              </button>
              
              <button
                v-if="authStore.isAdmin"
                @click="goToAdmin"
                class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
              >
                Go to Admin Panel
              </button>
              
              <button
                @click="goHome"
                class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
              >
                Go to Home
              </button>
              
              <button
                v-if="authStore.isAuthenticated"
                @click="logout"
                class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
              >
                Logout
              </button>
            </div>
          </div>

          <div v-if="message" class="p-4 rounded-lg" :class="messageClass">
            <p>{{ message }}</p>
          </div>

          <div class="p-4 bg-gray-50 rounded-lg">
            <h2 class="text-lg font-semibold text-gray-900 mb-2">Admin Panel Links</h2>
            <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
              <a href="/admin" class="block p-3 bg-white rounded border hover:bg-gray-50">
                Dashboard
              </a>
              <a href="/admin/products" class="block p-3 bg-white rounded border hover:bg-gray-50">
                Products
              </a>
              <a href="/admin/categories" class="block p-3 bg-white rounded border hover:bg-gray-50">
                Categories
              </a>
              <a href="/admin/orders" class="block p-3 bg-white rounded border hover:bg-gray-50">
                Orders
              </a>
              <a href="/admin/settings" class="block p-3 bg-white rounded border hover:bg-gray-50">
                Settings
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const authStore = useAuthStore()
const router = useRouter()

const message = ref('')
const messageClass = ref('')

const showMessage = (text, type = 'info') => {
  message.value = text
  messageClass.value = type === 'error' ? 'bg-red-50 text-red-900' : 'bg-blue-50 text-blue-900'
  
  setTimeout(() => {
    message.value = ''
  }, 3000)
}

const quickLogin = async () => {
  try {
    await authStore.login('<EMAIL>', 'admin123')
    showMessage('Successfully logged in as admin!')
  } catch (error) {
    showMessage('Login failed: ' + error.message, 'error')
  }
}

const goToAdmin = () => {
  router.push('/admin')
}

const goHome = () => {
  router.push('/')
}

const logout = async () => {
  await authStore.logout()
  showMessage('Logged out successfully')
}

useHead({
  title: 'Admin Test - Ecommerce Store'
})
</script>
