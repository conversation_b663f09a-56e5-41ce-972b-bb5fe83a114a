{"name": "@csstools/cascade-layer-name-parser", "description": "Parse CSS Cascade Layer names.", "version": "1.0.13", "contributors": [{"name": "Antonio <PERSON>", "email": "<EMAIL>", "url": "https://antonio.laguna.es"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "engines": {"node": "^14 || ^16 || >=18"}, "type": "module", "main": "dist/index.cjs", "module": "dist/index.mjs", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist"], "peerDependencies": {"@csstools/css-parser-algorithms": "^2.7.1", "@csstools/css-tokenizer": "^2.4.1"}, "scripts": {}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/cascade-layer-name-parser#readme", "repository": {"type": "git", "url": "git+https://github.com/csstools/postcss-plugins.git", "directory": "packages/cascade-layer-name-parser"}, "bugs": "https://github.com/csstools/postcss-plugins/issues", "keywords": ["cascade-layer", "css", "parser"]}