import { createPersistedState } from "pinia-plugin-persistedstate";

export default defineNuxtPlugin(({ $pinia }) => {
  if (process.client) {
    $pinia.use(
      createPersistedState({
        storage: {
          getItem: (key: string) => {
            return localStorage.getItem(key);
          },
          setItem: (key: string, value: string) => {
            localStorage.setItem(key, value);
          },
          removeItem: (key: string) => {
            localStorage.removeItem(key);
          },
        },
      })
    );
  }
});
