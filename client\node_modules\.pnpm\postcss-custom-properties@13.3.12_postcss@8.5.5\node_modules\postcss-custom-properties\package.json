{"name": "postcss-custom-properties", "description": "Use Custom Properties Queries in CSS", "version": "13.3.12", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON><PERSON>"], "license": "MIT", "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "engines": {"node": "^14 || ^16 || >=18"}, "type": "module", "main": "dist/index.cjs", "module": "dist/index.mjs", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist", "index.d.ts"], "dependencies": {"@csstools/cascade-layer-name-parser": "^1.0.13", "@csstools/css-parser-algorithms": "^2.7.1", "@csstools/css-tokenizer": "^2.4.1", "@csstools/utilities": "^1.0.0", "postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.4"}, "scripts": {}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-custom-properties#readme", "repository": {"type": "git", "url": "git+https://github.com/csstools/postcss-plugins.git", "directory": "plugins/postcss-custom-properties"}, "bugs": "https://github.com/csstools/postcss-plugins/issues", "keywords": ["css", "csswg", "custom", "declarations", "postcss", "postcss-plugin", "properties", "specification", "variables", "vars", "w3c"]}