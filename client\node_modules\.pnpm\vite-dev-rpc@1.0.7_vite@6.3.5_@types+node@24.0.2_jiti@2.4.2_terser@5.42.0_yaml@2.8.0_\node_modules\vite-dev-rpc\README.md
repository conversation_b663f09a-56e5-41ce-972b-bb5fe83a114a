# vite-dev-rpc

[![NPM version](https://img.shields.io/npm/v/vite-dev-rpc?color=a1b858&label=)](https://www.npmjs.com/package/vite-dev-rpc)

Remote procedure call for client-server communication in Vite plugins.

> Requires Vite ^2.9.0-beta.9

Based on

- [`birpc`](https://github.com/antfu/birpc) - Message-based two-way remote procedure call.
- [`vite-hot-client`](https://github.com/antfu/vite-hot-client) - Get `import.meta.hot` at runtime.
- [`import.meta.hot.send` API in Vite 2.9](https://github.com/vitejs/vite/pull/7437) - Server-client communication support.

## Sponsors

<p align="center">
  <a href="https://cdn.jsdelivr.net/gh/antfu/static/sponsors.svg">
    <img src='https://cdn.jsdelivr.net/gh/antfu/static/sponsors.svg'/>
  </a>
</p>

## License

[MIT](./LICENSE) License © 2021 [<PERSON>](https://github.com/antfu)
