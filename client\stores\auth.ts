import { defineStore } from "pinia";

interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  role: "USER" | "ADMIN";
}

interface AuthState {
  user: User | null;
  token: string | null;
  isLoading: boolean;
}

export const useAuthStore = defineStore("auth", {
  state: (): AuthState => ({
    user: null,
    token: null,
    isLoading: false,
  }),

  getters: {
    isAuthenticated: (state) => !!state.token && !!state.user,
    isAdmin: (state) => state.user?.role === "ADMIN",
    fullName: (state) => {
      if (!state.user) return "";
      return (
        `${state.user.firstName || ""} ${state.user.lastName || ""}`.trim() ||
        state.user.email
      );
    },
  },

  actions: {
    async login(email: string, password: string) {
      this.isLoading = true;
      try {
        const { $api } = useNuxtApp();
        const response = await $api("/auth/login", {
          method: "POST",
          body: { email, password },
        });

        this.token = response.token;
        this.user = response.user;

        // Set token for future requests
        this.setAuthHeader();

        return response;
      } catch (error) {
        console.error("Login error:", error);
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    async register(userData: {
      email: string;
      password: string;
      firstName?: string;
      lastName?: string;
    }) {
      this.isLoading = true;
      try {
        const { $api } = useNuxtApp();
        const response = await $api("/auth/register", {
          method: "POST",
          body: userData,
        });

        this.token = response.token;
        this.user = response.user;

        // Set token for future requests
        this.setAuthHeader();

        return response;
      } catch (error) {
        console.error("Register error:", error);
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    async fetchUser() {
      if (!this.token) return;

      try {
        const { $api } = useNuxtApp();
        const response = await $api("/auth/me");
        this.user = response.user;
      } catch (error) {
        console.error("Fetch user error:", error);
        this.logout();
      }
    },

    async updateProfile(userData: {
      firstName?: string;
      lastName?: string;
      email?: string;
    }) {
      this.isLoading = true;
      try {
        const { $api } = useNuxtApp();
        const response = await $api("/auth/profile", {
          method: "PUT",
          body: userData,
        });

        this.user = response.user;
        return response;
      } catch (error) {
        console.error("Update profile error:", error);
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    async changePassword(currentPassword: string, newPassword: string) {
      this.isLoading = true;
      try {
        const { $api } = useNuxtApp();
        const response = await $api("/auth/password", {
          method: "PUT",
          body: { currentPassword, newPassword },
        });

        return response;
      } catch (error) {
        console.error("Change password error:", error);
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    logout() {
      this.user = null;
      this.token = null;

      // Clear auth header
      const { $api } = useNuxtApp();
      if ($api.defaults?.headers) {
        delete $api.defaults.headers.authorization;
      }

      // Redirect to home
      navigateTo("/");
    },

    setAuthHeader() {
      if (this.token) {
        const { $api } = useNuxtApp();
        if ($api.defaults?.headers) {
          $api.defaults.headers.authorization = `Bearer ${this.token}`;
        }
      }
    },

    initializeAuth() {
      // This will be called on app initialization
      if (this.token) {
        this.setAuthHeader();
        this.fetchUser();
      }
    },
  },

  persist: {
    paths: ["user", "token"],
  },
});
