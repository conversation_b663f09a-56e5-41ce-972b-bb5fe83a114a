#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/replace-in-file@6.3.5/node_modules/replace-in-file/bin/node_modules:/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/replace-in-file@6.3.5/node_modules/replace-in-file/node_modules:/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/replace-in-file@6.3.5/node_modules:/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/replace-in-file@6.3.5/node_modules/replace-in-file/bin/node_modules:/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/replace-in-file@6.3.5/node_modules/replace-in-file/node_modules:/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/replace-in-file@6.3.5/node_modules:/mnt/c/Users/<USER>/OneDrive/Töölaud/ecommerce/client/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../replace-in-file@6.3.5/node_modules/replace-in-file/bin/cli.js" "$@"
else
  exec node  "$basedir/../../../../../replace-in-file@6.3.5/node_modules/replace-in-file/bin/cli.js" "$@"
fi
