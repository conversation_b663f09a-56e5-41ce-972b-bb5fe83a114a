<template>
  <div>
    <!-- <PERSON>er -->
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">Categories</h1>
        <p class="mt-2 text-gray-600">Manage your product categories</p>
      </div>
      <UButton @click="showCreateModal = true" icon="i-heroicons-plus">
        Add Category
      </UButton>
    </div>

    <!-- Categories Table -->
    <UCard>
      <UTable
        :rows="categories"
        :columns="columns"
        :loading="isLoading"
        :empty-state="{ icon: 'i-heroicons-tag', label: 'No categories found' }"
      >
        <template #name-data="{ row }">
          <div>
            <p class="font-medium text-gray-900">{{ row.name }}</p>
            <p class="text-sm text-gray-500">{{ row.slug }}</p>
          </div>
        </template>

        <template #description-data="{ row }">
          <p class="text-sm text-gray-600 max-w-xs truncate">
            {{ row.description || 'No description' }}
          </p>
        </template>

        <template #products-data="{ row }">
          <span class="text-sm text-gray-500">{{ row._count?.products || 0 }} products</span>
        </template>

        <template #sortOrder-data="{ row }">
          <span class="text-sm text-gray-500">{{ row.sortOrder }}</span>
        </template>

        <template #actions-data="{ row }">
          <UDropdown :items="getActionItems(row)">
            <UButton
              variant="ghost"
              icon="i-heroicons-ellipsis-horizontal"
            />
          </UDropdown>
        </template>
      </UTable>
    </UCard>

    <!-- Create/Edit Modal -->
    <UModal v-model="showCreateModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">
            {{ editingCategory ? 'Edit Category' : 'Create Category' }}
          </h3>
        </template>

        <form @submit.prevent="handleSubmit" class="space-y-4">
          <UFormGroup label="Category Name" required>
            <UInput
              v-model="categoryForm.name"
              placeholder="Enter category name"
              required
            />
          </UFormGroup>

          <UFormGroup label="Description">
            <UTextarea
              v-model="categoryForm.description"
              placeholder="Category description"
              :rows="3"
            />
          </UFormGroup>

          <UFormGroup label="Sort Order">
            <UInput
              v-model="categoryForm.sortOrder"
              type="number"
              placeholder="0"
            />
          </UFormGroup>

          <div class="flex justify-end space-x-3 pt-4">
            <UButton
              @click="closeModal"
              variant="outline"
            >
              Cancel
            </UButton>
            <UButton
              type="submit"
              :loading="isSubmitting"
            >
              {{ editingCategory ? 'Update' : 'Create' }}
            </UButton>
          </div>
        </form>
      </UCard>
    </UModal>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'admin'
})

// Reactive data
const categories = ref([])
const isLoading = ref(true)
const showCreateModal = ref(false)
const isSubmitting = ref(false)
const editingCategory = ref(null)

const categoryForm = ref({
  name: '',
  description: '',
  sortOrder: 0
})

// Table columns
const columns = [
  { key: 'name', label: 'Name' },
  { key: 'description', label: 'Description' },
  { key: 'products', label: 'Products' },
  { key: 'sortOrder', label: 'Sort Order' },
  { key: 'actions', label: 'Actions' }
]

// Action items for dropdown
const getActionItems = (category) => [
  [{
    label: 'Edit',
    icon: 'i-heroicons-pencil',
    click: () => editCategory(category)
  }],
  [{
    label: 'Delete',
    icon: 'i-heroicons-trash',
    click: () => deleteCategory(category.id)
  }]
]

// Generate slug from name
watch(() => categoryForm.value.name, (newName) => {
  if (!editingCategory.value) {
    categoryForm.value.slug = newName
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '')
  }
})

// Fetch categories
const fetchCategories = async () => {
  try {
    isLoading.value = true
    const { $api } = useNuxtApp()
    
    const response = await $api('/admin/categories')
    categories.value = response.categories || []
    
  } catch (error) {
    console.error('Failed to fetch categories:', error)
  } finally {
    isLoading.value = false
  }
}

// Handle form submission
const handleSubmit = async () => {
  try {
    isSubmitting.value = true
    const { $api } = useNuxtApp()
    
    const data = {
      ...categoryForm.value,
      slug: categoryForm.value.name
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '')
    }
    
    if (editingCategory.value) {
      await $api(`/admin/categories/${editingCategory.value.id}`, {
        method: 'PUT',
        body: data
      })
    } else {
      await $api('/admin/categories', {
        method: 'POST',
        body: data
      })
    }
    
    await fetchCategories()
    closeModal()
    
  } catch (error) {
    console.error('Failed to save category:', error)
  } finally {
    isSubmitting.value = false
  }
}

// Edit category
const editCategory = (category) => {
  editingCategory.value = category
  categoryForm.value = {
    name: category.name,
    description: category.description || '',
    sortOrder: category.sortOrder || 0
  }
  showCreateModal.value = true
}

// Delete category
const deleteCategory = async (categoryId) => {
  if (!confirm('Are you sure you want to delete this category?')) return
  
  try {
    const { $api } = useNuxtApp()
    await $api(`/admin/categories/${categoryId}`, { method: 'DELETE' })
    await fetchCategories()
  } catch (error) {
    console.error('Failed to delete category:', error)
  }
}

// Close modal
const closeModal = () => {
  showCreateModal.value = false
  editingCategory.value = null
  categoryForm.value = {
    name: '',
    description: '',
    sortOrder: 0
  }
}

// Load data on mount
onMounted(() => {
  fetchCategories()
})

// SEO
useHead({
  title: 'Categories - Admin Panel'
})
</script>
