import { defineStore } from "pinia";

interface CartItem {
  id: string;
  productId: string;
  variantId?: string;
  quantity: number;
  product: {
    id: string;
    name: string;
    slug: string;
    price: number;
    images: Array<{ url: string; alt?: string }>;
  };
  variant?: {
    id: string;
    name: string;
    value: string;
    type: string;
    price?: number;
  };
}

interface CartState {
  items: CartItem[];
  isOpen: boolean;
  isLoading: boolean;
  subtotal: number;
  itemCount: number;
}

export const useCartStore = defineStore("cart", {
  state: (): CartState => ({
    items: [],
    isOpen: false,
    isLoading: false,
    subtotal: 0,
    itemCount: 0,
  }),

  getters: {
    total: (state) => state.subtotal,
    isEmpty: (state) => state.items.length === 0,

    getItemPrice: () => (item: CartItem) => {
      return item.variant?.price || item.product.price;
    },

    getItemTotal: () => (item: CartItem) => {
      const price = item.variant?.price || item.product.price;
      return price * item.quantity;
    },
  },

  actions: {
    async fetchCart() {
      const authStore = useAuthStore();
      if (!authStore.isAuthenticated) {
        this.items = [];
        this.subtotal = 0;
        this.itemCount = 0;
        return;
      }

      this.isLoading = true;
      try {
        const { $api } = useNuxtApp();
        const response = await $api("/cart");

        this.items = response.items;
        this.subtotal = parseFloat(response.subtotal);
        this.itemCount = response.itemCount;
      } catch (error) {
        console.error("Fetch cart error:", error);
      } finally {
        this.isLoading = false;
      }
    },

    async addItem(productId: string, quantity: number = 1, variantId?: string) {
      const authStore = useAuthStore();
      if (!authStore.isAuthenticated) {
        throw new Error("Please log in to add items to cart");
      }

      this.isLoading = true;
      try {
        const { $api } = useNuxtApp();
        const response = await $api("/cart/add", {
          method: "POST",
          body: { productId, quantity, variantId },
        });

        // Refresh cart
        await this.fetchCart();

        // Show success message
        const toast = useToast();
        toast.add({
          title: "Added to cart",
          description: response.message,
          color: "green",
        });

        return response;
      } catch (error: any) {
        console.error("Add to cart error:", error);

        const toast = useToast();
        toast.add({
          title: "Error",
          description: error.data?.error || "Failed to add item to cart",
          color: "red",
        });

        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    async updateQuantity(itemId: string, quantity: number) {
      this.isLoading = true;
      try {
        const { $api } = useNuxtApp();
        await $api(`/cart/${itemId}`, {
          method: "PUT",
          body: { quantity },
        });

        // Refresh cart
        await this.fetchCart();
      } catch (error: any) {
        console.error("Update cart error:", error);

        const toast = useToast();
        toast.add({
          title: "Error",
          description: error.data?.error || "Failed to update cart",
          color: "red",
        });

        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    async removeItem(itemId: string) {
      this.isLoading = true;
      try {
        const { $api } = useNuxtApp();
        await $api(`/cart/${itemId}`, {
          method: "DELETE",
        });

        // Refresh cart
        await this.fetchCart();

        const toast = useToast();
        toast.add({
          title: "Removed from cart",
          color: "green",
        });
      } catch (error: any) {
        console.error("Remove from cart error:", error);

        const toast = useToast();
        toast.add({
          title: "Error",
          description: error.data?.error || "Failed to remove item",
          color: "red",
        });

        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    async clearCart() {
      this.isLoading = true;
      try {
        const { $api } = useNuxtApp();
        await $api("/cart", {
          method: "DELETE",
        });

        this.items = [];
        this.subtotal = 0;
        this.itemCount = 0;

        const toast = useToast();
        toast.add({
          title: "Cart cleared",
          color: "green",
        });
      } catch (error: any) {
        console.error("Clear cart error:", error);

        const toast = useToast();
        toast.add({
          title: "Error",
          description: error.data?.error || "Failed to clear cart",
          color: "red",
        });

        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    openCart() {
      this.isOpen = true;
    },

    closeCart() {
      this.isOpen = false;
    },

    toggleCart() {
      this.isOpen = !this.isOpen;
    },
  },

  persist: {
    paths: ["items", "subtotal", "itemCount"],
  },
});
