<template>
  <div>
    <!-- <PERSON>er -->
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">Add New Product</h1>
        <p class="mt-2 text-gray-600">Create a new product for your store</p>
      </div>
      <UButton
        to="/admin/products"
        variant="outline"
        icon="i-heroicons-arrow-left"
      >
        Back to Products
      </UButton>
    </div>

    <!-- Product Form -->
    <form @submit.prevent="handleSubmit">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
          <!-- Basic Information -->
          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold">Basic Information</h3>
            </template>

            <div class="space-y-4">
              <UFormGroup label="Product Name" required>
                <UInput
                  v-model="form.name"
                  placeholder="Enter product name"
                  required
                />
              </UFormGroup>

              <UFormGroup label="Short Description">
                <UInput
                  v-model="form.shortDesc"
                  placeholder="Brief description for product cards"
                />
              </UFormGroup>

              <UFormGroup label="Full Description">
                <UTextarea
                  v-model="form.description"
                  placeholder="Detailed product description"
                  :rows="4"
                />
              </UFormGroup>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <UFormGroup label="SKU">
                  <UInput v-model="form.sku" placeholder="Product SKU" />
                </UFormGroup>

                <UFormGroup label="Weight (kg)">
                  <UInput
                    v-model="form.weight"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                  />
                </UFormGroup>
              </div>

              <UFormGroup label="Dimensions">
                <UInput
                  v-model="form.dimensions"
                  placeholder="L x W x H (cm)"
                />
              </UFormGroup>
            </div>
          </UCard>

          <!-- Pricing -->
          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold">Pricing</h3>
            </template>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <UFormGroup label="Price" required>
                <UInput
                  v-model="form.price"
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  required
                />
              </UFormGroup>

              <UFormGroup label="Compare Price">
                <UInput
                  v-model="form.comparePrice"
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                />
              </UFormGroup>
            </div>
          </UCard>

          <!-- Images -->
          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold">Product Images</h3>
            </template>

            <div class="space-y-4">
              <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div
                  v-for="(image, index) in form.images"
                  :key="index"
                  class="relative group"
                >
                  <img
                    :src="image.url"
                    :alt="image.alt"
                    class="w-full h-32 object-cover rounded-lg border"
                  />
                  <UButton
                    @click="removeImage(index)"
                    variant="solid"
                    color="red"
                    size="xs"
                    icon="i-heroicons-x-mark"
                    class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                  />
                </div>
              </div>

              <div
                class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center"
              >
                <UIcon
                  name="i-heroicons-photo"
                  class="h-12 w-12 text-gray-400 mx-auto mb-4"
                />
                <p class="text-gray-500 mb-4">Add product images</p>
                <UInput
                  v-model="newImageUrl"
                  placeholder="Enter image URL"
                  class="mb-2"
                />
                <UButton @click="addImage" variant="outline" size="sm">
                  Add Image
                </UButton>
              </div>
            </div>
          </UCard>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
          <!-- Status -->
          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold">Status</h3>
            </template>

            <div class="space-y-4">
              <UFormGroup>
                <UCheckbox v-model="form.isActive" label="Active" />
              </UFormGroup>

              <UFormGroup>
                <UCheckbox v-model="form.isFeatured" label="Featured Product" />
              </UFormGroup>
            </div>
          </UCard>

          <!-- Category -->
          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold">Category</h3>
            </template>

            <UFormGroup label="Category" required>
              <USelect
                v-model="form.categoryId"
                :options="categoryOptions"
                placeholder="Select category"
                required
              />
            </UFormGroup>
          </UCard>

          <!-- Inventory -->
          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold">Inventory</h3>
            </template>

            <UFormGroup label="Stock Quantity" required>
              <UInput
                v-model="form.stock"
                type="number"
                placeholder="0"
                required
              />
            </UFormGroup>
          </UCard>

          <!-- Actions -->
          <UCard>
            <div class="space-y-3">
              <UButton type="submit" :loading="isLoading" block>
                Create Product
              </UButton>

              <UButton to="/admin/products" variant="outline" block>
                Cancel
              </UButton>
            </div>
          </UCard>
        </div>
      </div>
    </form>
  </div>
</template>

<script setup>
definePageMeta({
  layout: "admin",
});

const router = useRouter();

// Form data
const form = ref({
  name: "",
  slug: "",
  description: "",
  shortDesc: "",
  price: "",
  comparePrice: "",
  sku: "",
  stock: 0,
  isActive: true,
  isFeatured: false,
  weight: "",
  dimensions: "",
  categoryId: "",
  images: [],
});

const newImageUrl = ref("");
const isLoading = ref(false);
const categoryOptions = ref([]);

// Generate slug from name
watch(
  () => form.value.name,
  (newName) => {
    form.value.slug = newName
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "");
  }
);

// Add image
const addImage = () => {
  if (newImageUrl.value.trim()) {
    form.value.images.push({
      url: newImageUrl.value.trim(),
      alt: form.value.name || "Product image",
      sortOrder: form.value.images.length,
    });
    newImageUrl.value = "";
  }
};

// Remove image
const removeImage = (index) => {
  form.value.images.splice(index, 1);
  // Update sort orders
  form.value.images.forEach((img, idx) => {
    img.sortOrder = idx;
  });
};

// Handle form submission
const handleSubmit = async () => {
  try {
    isLoading.value = true;
    const { $api } = useNuxtApp();

    await $api("/admin/products", {
      method: "POST",
      body: form.value,
    });

    await router.push("/admin/products");
  } catch (error) {
    console.error("Failed to create product:", error);
  } finally {
    isLoading.value = false;
  }
};

// Fetch categories
const fetchCategories = async () => {
  try {
    const { $api } = useNuxtApp();
    const response = await $api("/admin/categories");
    const categories = response.categories || [];

    categoryOptions.value = categories.map((cat) => ({
      label: cat.name,
      value: cat.id,
    }));
  } catch (error) {
    console.error("Failed to fetch categories:", error);
  }
};

// Load data on mount
onMounted(() => {
  fetchCategories();
});

// SEO
useHead({
  title: "Add Product - Admin Panel",
});
</script>
