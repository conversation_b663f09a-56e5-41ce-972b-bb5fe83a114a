function hasFallback(e){const t=e.parent;if(!t)return!1;const r=e.prop.toLowerCase(),o=t.index(e);for(let e=0;e<o;e++){const o=t.nodes[e];if("decl"===o.type&&o.prop.toLowerCase()===r)return!0}return!1}function hasSupportsAtRuleAncestor(e,t){let r=e.parent;for(;r;)if("atrule"===r.type&&"supports"===r.name.toLowerCase()){if(t.test(r.params))return!0;r=r.parent}else r=r.parent;return!1}export{hasFallback,hasSupportsAtRuleAncestor};
