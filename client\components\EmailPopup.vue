<template>
  <div
    v-if="siteStore.showEmailPopup"
    class="modal-overlay"
    @click.self="closePopup"
  >
    <div class="modal-content scale-in">
      <!-- Close button -->
      <button
        @click="closePopup"
        class="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
      >
        <UIcon name="i-heroicons-x-mark" class="w-6 h-6" />
      </button>

      <!-- Content -->
      <div class="text-center">
        <!-- Icon -->
        <div
          class="mx-auto flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-6"
        >
          <UIcon name="i-heroicons-envelope" class="w-8 h-8 text-blue-600" />
        </div>

        <!-- Title -->
        <h3 class="text-2xl font-bold text-gray-900 mb-4">
          Get 15% Off Your First Order!
        </h3>

        <!-- Description -->
        <p class="text-gray-600 mb-6">
          Subscribe to our newsletter and receive exclusive offers, new product
          updates, and style tips.
        </p>

        <!-- Form -->
        <form @submit.prevent="subscribe" class="space-y-4">
          <div>
            <input
              v-model="email"
              type="email"
              placeholder="Enter your email address"
              class="form-input"
              required
              :disabled="isLoading"
            />
          </div>

          <button
            type="submit"
            :disabled="isLoading || !email"
            class="form-button w-full"
          >
            <span v-if="isLoading" class="flex items-center justify-center">
              <div class="loading-spinner mr-2" />
              Subscribing...
            </span>
            <span v-else> Get My 15% Discount </span>
          </button>
        </form>

        <!-- Terms -->
        <p class="text-xs text-gray-500 mt-4">
          By subscribing, you agree to our
          <NuxtLink to="/privacy" class="underline hover:text-gray-700"
            >Privacy Policy</NuxtLink
          >
          and
          <NuxtLink to="/terms" class="underline hover:text-gray-700"
            >Terms of Service</NuxtLink
          >. You can unsubscribe at any time.
        </p>

        <!-- Skip option -->
        <button
          @click="closePopup"
          class="text-sm text-gray-500 hover:text-gray-700 mt-4 underline"
        >
          No thanks, I'll pay full price
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
const siteStore = useSiteStore();

const email = ref("");
const isLoading = ref(false);

const subscribe = async () => {
  if (!email.value) return;

  isLoading.value = true;
  try {
    await siteStore.subscribeToNewsletter(email.value, "popup");
    // Success handled in store
  } catch (error) {
    // Error handled in store
  } finally {
    isLoading.value = false;
  }
};

const closePopup = () => {
  siteStore.hideEmailPopup();
};

// Close on escape key
onMounted(() => {
  const handleEscape = (e) => {
    if (e.key === "Escape") {
      closePopup();
    }
  };

  document.addEventListener("keydown", handleEscape);

  onUnmounted(() => {
    document.removeEventListener("keydown", handleEscape);
  });
});
</script>
