const express = require('express');
const { PrismaClient } = require('@prisma/client');

const router = express.Router();
const prisma = new PrismaClient();

// Get active slideshows
router.get('/slideshows', async (req, res) => {
  try {
    const slideshows = await prisma.slideshow.findMany({
      where: { isActive: true },
      orderBy: { sortOrder: 'asc' }
    });

    res.json({ slideshows });
  } catch (error) {
    console.error('Get public slideshows error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get site settings
router.get('/settings', async (req, res) => {
  try {
    const settings = await prisma.siteSettings.findMany();
    
    const settingsObj = {};
    settings.forEach(setting => {
      settingsObj[setting.key] = setting.value;
    });

    res.json({ settings: settingsObj });
  } catch (error) {
    console.error('Get public settings error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
