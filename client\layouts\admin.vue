<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Ad<PERSON>er -->
    <header class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo and Title -->
          <div class="flex items-center">
            <NuxtLink to="/admin" class="flex items-center space-x-3">
              <img src="/logo.svg" alt="Logo" class="h-8 w-auto" />
              <span class="text-xl font-bold text-gray-900">Admin Panel</span>
            </NuxtLink>
          </div>

          <!-- User Menu -->
          <div class="flex items-center space-x-4">
            <UButton
              to="/"
              variant="ghost"
              icon="i-heroicons-home"
              label="View Store"
            />
            
            <UDropdown :items="userMenuItems">
              <UButton
                variant="ghost"
                :label="authStore.userDisplayName"
                trailing-icon="i-heroicons-chevron-down-20-solid"
              />
            </UDropdown>
          </div>
        </div>
      </div>
    </header>

    <div class="flex">
      <!-- Sidebar -->
      <aside class="w-64 bg-white shadow-sm min-h-screen">
        <nav class="mt-8 px-4">
          <div class="space-y-2">
            <NuxtLink
              v-for="item in navigationItems"
              :key="item.name"
              :to="item.href"
              class="group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors"
              :class="[
                $route.path === item.href
                  ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                  : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
              ]"
            >
              <UIcon
                :name="item.icon"
                class="mr-3 h-5 w-5"
                :class="[
                  $route.path === item.href
                    ? 'text-blue-500'
                    : 'text-gray-400 group-hover:text-gray-500'
                ]"
              />
              {{ item.name }}
            </NuxtLink>
          </div>
        </nav>
      </aside>

      <!-- Main Content -->
      <main class="flex-1 p-8">
        <slot />
      </main>
    </div>
  </div>
</template>

<script setup>
const authStore = useAuthStore()
const router = useRouter()

// Check if user is admin
onMounted(() => {
  if (!authStore.user || authStore.user.role !== 'ADMIN') {
    router.push('/login')
  }
})

// Navigation items
const navigationItems = [
  {
    name: 'Dashboard',
    href: '/admin',
    icon: 'i-heroicons-home'
  },
  {
    name: 'Products',
    href: '/admin/products',
    icon: 'i-heroicons-cube'
  },
  {
    name: 'Categories',
    href: '/admin/categories',
    icon: 'i-heroicons-tag'
  },
  {
    name: 'Orders',
    href: '/admin/orders',
    icon: 'i-heroicons-shopping-bag'
  },
  {
    name: 'Users',
    href: '/admin/users',
    icon: 'i-heroicons-users'
  },
  {
    name: 'Slideshows',
    href: '/admin/slideshows',
    icon: 'i-heroicons-photo'
  },
  {
    name: 'Settings',
    href: '/admin/settings',
    icon: 'i-heroicons-cog-6-tooth'
  }
]

// User menu items
const userMenuItems = [
  [{
    label: 'Profile',
    icon: 'i-heroicons-user',
    click: () => router.push('/admin/profile')
  }],
  [{
    label: 'Logout',
    icon: 'i-heroicons-arrow-right-on-rectangle',
    click: () => authStore.logout()
  }]
]

// SEO
useHead({
  title: 'Admin Panel - Ecommerce Store'
})
</script>
