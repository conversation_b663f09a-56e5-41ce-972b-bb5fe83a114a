{"name": "@csstools/selector-specificity", "description": "Determine selector specificity with postcss-selector-parser", "version": "3.1.1", "contributors": [{"name": "Antonio <PERSON>", "email": "<EMAIL>", "url": "https://antonio.laguna.es"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT-0", "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "engines": {"node": "^14 || ^16 || >=18"}, "type": "module", "main": "dist/index.cjs", "module": "dist/index.mjs", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist"], "peerDependencies": {"postcss-selector-parser": "^6.0.13"}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/selector-specificity#readme", "repository": {"type": "git", "url": "git+https://github.com/csstools/postcss-plugins.git", "directory": "packages/selector-specificity"}, "bugs": "https://github.com/csstools/postcss-plugins/issues", "keywords": ["css", "postcss-selector-parser", "specificity"]}