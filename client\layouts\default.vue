<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Notification Bar -->
    <div
      v-if="
        siteStore.showNotificationBar &&
        siteStore.settings.notifications?.message
      "
      class="notification-bar"
    >
      <div
        class="container mx-auto container-padding flex items-center justify-between"
      >
        <span>{{ siteStore.settings.notifications.message }}</span>
        <button
          @click="siteStore.showNotificationBar = false"
          class="text-white hover:text-gray-200"
        >
          <UIcon name="i-heroicons-x-mark" />
        </button>
      </div>
    </div>

    <!-- Header -->
    <header class="header-bar">
      <div class="container mx-auto container-padding">
        <div class="flex items-center justify-between h-16">
          <!-- Logo -->
          <NuxtLink to="/" class="logo-container">
            <img
              v-if="siteStore.siteLogo"
              :src="siteStore.siteLogo"
              :alt="siteStore.siteName"
              class="h-8 w-auto"
            />
            <span v-else class="text-xl font-bold text-gray-900">
              {{ siteStore.siteName }}
            </span>
          </NuxtLink>

          <!-- Navigation -->
          <nav class="hidden md:flex items-center space-x-8">
            <NuxtLink
              to="/"
              class="text-gray-700 hover:text-gray-900 font-medium transition-colors"
            >
              Home
            </NuxtLink>
            <NuxtLink
              to="/products"
              class="text-gray-700 hover:text-gray-900 font-medium transition-colors"
            >
              Products
            </NuxtLink>
            <NuxtLink
              to="/categories"
              class="text-gray-700 hover:text-gray-900 font-medium transition-colors"
            >
              Categories
            </NuxtLink>
          </nav>

          <!-- Right side actions -->
          <div class="flex items-center space-x-4">
            <!-- Search -->
            <button class="text-gray-700 hover:text-gray-900">
              <UIcon name="i-heroicons-magnifying-glass" class="w-5 h-5" />
            </button>

            <!-- User menu -->
            <div v-if="authStore.isAuthenticated" class="relative group">
              <button
                class="flex items-center space-x-2 text-gray-700 hover:text-gray-900"
              >
                <span class="w-5 h-5 text-center">👤</span>
                <span class="hidden sm:block">{{ authStore.fullName }}</span>
                <span class="w-4 h-4 text-center">▼</span>
              </button>

              <!-- Dropdown menu -->
              <div
                class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200"
              >
                <NuxtLink
                  to="/profile"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  Profile
                </NuxtLink>
                <NuxtLink
                  to="/orders"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  Orders
                </NuxtLink>
                <div v-if="authStore.isAdmin">
                  <hr class="my-1" />
                  <NuxtLink
                    to="/admin"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    Admin Panel
                  </NuxtLink>
                </div>
                <hr class="my-1" />
                <button
                  @click="authStore.logout()"
                  class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  Logout
                </button>
              </div>
            </div>

            <!-- Login button -->
            <NuxtLink
              v-else
              to="/login"
              class="text-gray-700 hover:text-gray-900 flex items-center space-x-1"
            >
              <span class="w-5 h-5 text-center">👤</span>
              <span class="hidden sm:inline">Login</span>
            </NuxtLink>

            <!-- Cart button -->
            <button
              @click="cartStore.toggleCart()"
              class="relative text-gray-700 hover:text-gray-900 flex items-center space-x-1"
            >
              <span class="w-5 h-5 text-center">🛒</span>
              <span class="hidden sm:inline">Cart</span>
              <span
                v-if="cartStore.itemCount > 0"
                class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center"
              >
                {{ cartStore.itemCount }}
              </span>
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- Main content -->
    <main>
      <slot />
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white section-padding mt-16">
      <div class="container mx-auto container-padding">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <!-- Company info -->
          <div>
            <h3 class="text-lg font-semibold mb-4">{{ siteStore.siteName }}</h3>
            <p class="text-gray-400 mb-4">
              {{
                siteStore.settings.siteDescription ||
                "Beautiful ecommerce webstore with modern design"
              }}
            </p>
            <div v-if="siteStore.settings.socialMedia" class="flex space-x-4">
              <a
                v-if="siteStore.settings.socialMedia.facebook"
                :href="siteStore.settings.socialMedia.facebook"
                class="text-gray-400 hover:text-white"
              >
                📘 Facebook
              </a>
              <a
                v-if="siteStore.settings.socialMedia.instagram"
                :href="siteStore.settings.socialMedia.instagram"
                class="text-gray-400 hover:text-white"
              >
                📷 Instagram
              </a>
              <a
                v-if="siteStore.settings.socialMedia.twitter"
                :href="siteStore.settings.socialMedia.twitter"
                class="text-gray-400 hover:text-white"
              >
                🐦 Twitter
              </a>
            </div>
          </div>

          <!-- Quick links -->
          <div>
            <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
            <ul class="space-y-2">
              <li>
                <NuxtLink to="/products" class="text-gray-400 hover:text-white"
                  >Products</NuxtLink
                >
              </li>
              <li>
                <NuxtLink
                  to="/categories"
                  class="text-gray-400 hover:text-white"
                  >Categories</NuxtLink
                >
              </li>
              <li>
                <NuxtLink to="/about" class="text-gray-400 hover:text-white"
                  >About Us</NuxtLink
                >
              </li>
              <li>
                <NuxtLink to="/contact" class="text-gray-400 hover:text-white"
                  >Contact</NuxtLink
                >
              </li>
            </ul>
          </div>

          <!-- Customer service -->
          <div>
            <h3 class="text-lg font-semibold mb-4">Customer Service</h3>
            <ul class="space-y-2">
              <li>
                <NuxtLink to="/help" class="text-gray-400 hover:text-white"
                  >Help Center</NuxtLink
                >
              </li>
              <li>
                <NuxtLink to="/shipping" class="text-gray-400 hover:text-white"
                  >Shipping Info</NuxtLink
                >
              </li>
              <li>
                <NuxtLink to="/returns" class="text-gray-400 hover:text-white"
                  >Returns</NuxtLink
                >
              </li>
              <li>
                <NuxtLink to="/privacy" class="text-gray-400 hover:text-white"
                  >Privacy Policy</NuxtLink
                >
              </li>
              <li>
                <NuxtLink to="/terms" class="text-gray-400 hover:text-white"
                  >Terms of Service</NuxtLink
                >
              </li>
            </ul>
          </div>

          <!-- Newsletter -->
          <div>
            <h3 class="text-lg font-semibold mb-4">Newsletter</h3>
            <p class="text-gray-400 mb-4">
              Subscribe for updates and special offers
            </p>
            <form @submit.prevent="subscribeNewsletter" class="space-y-2">
              <input
                v-model="newsletterEmail"
                type="email"
                placeholder="Enter your email"
                class="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
              <button
                type="submit"
                class="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Subscribe
              </button>
            </form>
          </div>
        </div>

        <hr class="border-gray-800 my-8" />

        <div class="flex flex-col md:flex-row justify-between items-center">
          <p class="text-gray-400 text-sm">
            © {{ new Date().getFullYear() }} {{ siteStore.siteName }}. All
            rights reserved.
          </p>
          <div class="flex space-x-4 mt-4 md:mt-0">
            <img src="/payment-methods.svg" alt="Payment methods" class="h-6" />
          </div>
        </div>
      </div>
    </footer>

    <!-- Cart Drawer -->
    <CartDrawer />

    <!-- Email Popup -->
    <EmailPopup />

    <!-- GDPR Banner -->
    <GdprBanner />
  </div>
</template>

<script setup>
const authStore = useAuthStore();
const cartStore = useCartStore();
const siteStore = useSiteStore();

const newsletterEmail = ref("");

const subscribeNewsletter = async () => {
  if (!newsletterEmail.value) return;

  try {
    await siteStore.subscribeToNewsletter(newsletterEmail.value, "footer");
    newsletterEmail.value = "";
  } catch (error) {
    // Error handled in store
  }
};

// Initialize stores on mount
onMounted(() => {
  authStore.initializeAuth();
  cartStore.fetchCart();
  siteStore.initializeSite();
});
</script>
