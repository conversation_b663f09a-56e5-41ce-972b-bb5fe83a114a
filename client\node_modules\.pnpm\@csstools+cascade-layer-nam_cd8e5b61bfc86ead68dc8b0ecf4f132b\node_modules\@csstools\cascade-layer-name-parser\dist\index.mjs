import{isTokenIdent as e,TokenType as t,isTokenDelim as n,stringify as r,ParseError as a,isTokenWhiteSpaceOrComment as s,tokenizer as o,isTokenWhitespace as l,isTokenComment as i}from"@csstools/css-tokenizer";import{parseCommaSeparatedListOfComponentValues as c,isTokenNode as m,isCommentNode as u,isWhitespaceNode as p}from"@csstools/css-parser-algorithms";class LayerName{parts;constructor(e){this.parts=e}tokens(){return[...this.parts]}slice(t,n){const r=[];for(let t=0;t<this.parts.length;t++)e(this.parts[t])&&r.push(t);const a=r.slice(t,n);return new LayerName(this.parts.slice(a[0],a[a.length-1]+1))}concat(r){const a=[t.Delim,".",-1,-1,{value:"."}];return new LayerName([...this.parts.filter((t=>e(t)||n(t))),a,...r.parts.filter((t=>e(t)||n(t)))])}segments(){return this.parts.filter((t=>e(t))).map((e=>e[4].value))}name(){return this.parts.filter((t=>e(t)||n(t))).map((e=>e[1])).join("")}equal(e){const t=this.segments(),n=e.segments();if(t.length!==n.length)return!1;for(let e=0;e<t.length;e++){if(t[e]!==n[e])return!1}return!0}toString(){return r(...this.parts)}toJSON(){return{parts:this.parts,segments:this.segments(),name:this.name()}}}function addLayerToModel(e,t){t.forEach((t=>{const n=t.segments();e:for(let r=0;r<n.length;r++){const n=t.slice(0,r+1),a=n.segments();let s=-1,o=0;for(let t=0;t<e.length;t++){const n=e[t].segments();let r=0;t:for(let e=0;e<n.length;e++){const t=n[e],s=a[e];if(s===t&&e+1===a.length)continue e;if(s!==t){if(s!==t)break t}else r++}r>=o&&(s=t,o=r)}-1===s?e.push(n):e.splice(s+1,0,n)}}))}function parseFromTokens(t,r){const o=c(t,{onParseError:r?.onParseError}),f=r?.onParseError??(()=>{}),h=["6.4.2. Layer Naming and Nesting","Layer name syntax","<layer-name> = <ident> [ '.' <ident> ]*"],d=t[0][2],y=t[t.length-1][3],g=[];for(let t=0;t<o.length;t++){const r=o[t];for(let e=0;e<r.length;e++){const t=r[e];if(!m(t)&&!u(t)&&!p(t))return f(new a(`Invalid cascade layer name. Invalid layer name part "${t.toString()}"`,d,y,h)),[]}const c=r.flatMap((e=>e.tokens()));let w=!1,v=!1,L=null;for(let t=0;t<c.length;t++){const r=c[t];if(!(s(r)||e(r)||n(r)&&"."===r[4].value))return f(new a(`Invalid cascade layer name. Invalid character "${r[1]}"`,d,y,h)),[];if(!w&&n(r))return f(new a("Invalid cascade layer name. Layer names can not start with a dot.",d,y,h)),[];if(w){if(l(r)){v=!0;continue}if(v&&i(r))continue;if(v)return f(new a("Invalid cascade layer name. Encountered unexpected whitespace between layer name parts.",d,y,h)),[];if(e(L)&&e(r))return f(new a("Invalid cascade layer name. Layer name parts must be separated by dots.",d,y,h)),[];if(n(L)&&n(r))return f(new a("Invalid cascade layer name. Layer name parts must not be empty.",d,y,h)),[]}e(r)&&(w=!0),(e(r)||n(r))&&(L=r)}if(!L)return f(new a("Invalid cascade layer name. Empty layer name.",d,y,h)),[];if(n(L))return f(new a("Invalid cascade layer name. Layer name must not end with a dot.",d,y,h)),[];g.push(new LayerName(c))}return g}function parse(e,t){const n=o({css:e},{onParseError:t?.onParseError}),r=[];for(;!n.endOfFile();)r.push(n.nextToken());return r.push(n.nextToken()),parseFromTokens(r,t)}export{LayerName,addLayerToModel,parse,parseFromTokens};
