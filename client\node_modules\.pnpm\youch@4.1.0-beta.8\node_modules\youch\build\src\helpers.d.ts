import { Colors } from '@poppinss/colors/types';
/**
 * HTML escape string values so that they can be nested
 * inside the pre-tags.
 */
export declare function htmlEscape(value: string): string;
/**
 * Wraps a string value to be on multiple lines after
 * a certain characters limit has been hit.
 */
export declare function wordWrap(value: string, options: {
    width: number;
    indent: string;
    newLine: string;
    escape?: (value: string) => string;
}): string;
/**
 * Strips ANSI sequences from the string
 */
export declare function stripAnsi(value: string): string;
/**
 * ANSI coloring library
 */
export declare const colors: Colors;
