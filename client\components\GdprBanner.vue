<template>
  <div
    v-if="siteStore.shouldShowGdprBanner"
    class="fixed bottom-0 left-0 right-0 bg-gray-900 text-white p-4 z-50 slide-up"
  >
    <div class="container mx-auto container-padding">
      <div
        class="flex flex-col sm:flex-row items-start sm:items-center justify-between space-y-4 sm:space-y-0"
      >
        <!-- Message -->
        <div class="flex-1">
          <p class="text-sm">
            {{
              siteStore.settings.gdpr?.message ||
              "We use cookies to enhance your browsing experience and provide personalized content. By continuing to use our site, you consent to our use of cookies."
            }}
            <NuxtLink to="/privacy" class="underline hover:text-gray-300 ml-1">
              Learn more
            </NuxtLink>
          </p>
        </div>

        <!-- Actions -->
        <div class="flex items-center space-x-3">
          <button
            @click="acceptGdpr"
            class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg text-sm font-medium transition-colors"
          >
            Accept
          </button>

          <button
            @click="showSettings = true"
            class="text-gray-300 hover:text-white text-sm underline"
          >
            Settings
          </button>
        </div>
      </div>
    </div>

    <!-- Cookie Settings Modal -->
    <div
      v-if="showSettings"
      class="fixed inset-0 bg-black bg-opacity-50 z-60 flex items-center justify-center p-4"
      @click.self="showSettings = false"
    >
      <div
        class="bg-white rounded-xl shadow-2xl max-w-md w-full p-6 text-gray-900"
      >
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold">Cookie Settings</h3>
          <button
            @click="showSettings = false"
            class="text-gray-400 hover:text-gray-600"
          >
            <UIcon name="i-heroicons-x-mark" class="w-6 h-6" />
          </button>
        </div>

        <div class="space-y-4">
          <!-- Essential cookies -->
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <h4 class="font-medium mb-1">Essential Cookies</h4>
              <p class="text-sm text-gray-600">
                Required for the website to function properly. Cannot be
                disabled.
              </p>
            </div>
            <div class="ml-4">
              <input
                type="checkbox"
                checked
                disabled
                class="rounded border-gray-300"
              />
            </div>
          </div>

          <!-- Analytics cookies -->
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <h4 class="font-medium mb-1">Analytics Cookies</h4>
              <p class="text-sm text-gray-600">
                Help us understand how visitors interact with our website.
              </p>
            </div>
            <div class="ml-4">
              <input
                v-model="cookieSettings.analytics"
                type="checkbox"
                class="rounded border-gray-300"
              />
            </div>
          </div>

          <!-- Marketing cookies -->
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <h4 class="font-medium mb-1">Marketing Cookies</h4>
              <p class="text-sm text-gray-600">
                Used to deliver personalized advertisements and track campaign
                performance.
              </p>
            </div>
            <div class="ml-4">
              <input
                v-model="cookieSettings.marketing"
                type="checkbox"
                class="rounded border-gray-300"
              />
            </div>
          </div>
        </div>

        <div class="flex space-x-3 mt-6">
          <button
            @click="saveSettings"
            class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-lg font-medium transition-colors"
          >
            Save Settings
          </button>
          <button
            @click="acceptAll"
            class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-900 py-2 rounded-lg font-medium transition-colors"
          >
            Accept All
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const siteStore = useSiteStore();

const showSettings = ref(false);
const cookieSettings = ref({
  analytics: false,
  marketing: false,
});

const acceptGdpr = () => {
  // Accept all cookies by default
  saveCookiePreferences({
    essential: true,
    analytics: true,
    marketing: true,
  });

  siteStore.acceptGdpr();
};

const acceptAll = () => {
  cookieSettings.value = {
    analytics: true,
    marketing: true,
  };
  saveSettings();
};

const saveSettings = () => {
  saveCookiePreferences({
    essential: true,
    analytics: cookieSettings.value.analytics,
    marketing: cookieSettings.value.marketing,
  });

  showSettings.value = false;
  siteStore.acceptGdpr();
};

const saveCookiePreferences = (preferences) => {
  if (process.client) {
    localStorage.setItem("cookie-preferences", JSON.stringify(preferences));

    // Initialize analytics if accepted
    if (preferences.analytics) {
      // TODO: Initialize Google Analytics or other analytics
      console.log("Analytics cookies accepted");
    }

    // Initialize marketing if accepted
    if (preferences.marketing) {
      // TODO: Initialize marketing pixels, etc.
      console.log("Marketing cookies accepted");
    }
  }
};

// Load existing preferences
onMounted(() => {
  if (process.client) {
    const saved = localStorage.getItem("cookie-preferences");
    if (saved) {
      const preferences = JSON.parse(saved);
      cookieSettings.value = {
        analytics: preferences.analytics || false,
        marketing: preferences.marketing || false,
      };
    }
  }
});
</script>
