# vue-devtools-stub

[![npm version][npm-version-src]][npm-version-href]
[![npm downloads][npm-downloads-src]][npm-downloads-href]
[![Github Actions][github-actions-src]][github-actions-href]
[![Codecov][codecov-src]][codecov-href]

Stub package for [`@vue/devtools-api`](https://npmjs.com/package/@vue/devtools-api).

**Why this package:**

Many of Vue 3 libraries use this package internally to integrate with devtools and mostly for development.

While normally an __esm bundler__ version of those libraries allows tree-shaking and opting out, it is not always possible and specially problematic with externalization.

This package mocks same interface of the package.

## 💻 Development

- Clone this repository
- Enable [Corepack](https://github.com/nodejs/corepack) using `corepack enable` (use `npm i -g corepack` for Node.js < 16.10)
- Install dependencies using `pnpm install`
- Run interactive tests using `pnpm dev`

## License

Made with 💛

Published under [MIT License](./LICENSE).

<!-- Badges -->
[npm-version-src]: https://img.shields.io/npm/v/vue-devtools-stub?style=flat-square
[npm-version-href]: https://npmjs.com/package/vue-devtools-stub

[npm-downloads-src]: https://img.shields.io/npm/dm/vue-devtools-stub?style=flat-square
[npm-downloads-href]: https://npmjs.com/package/vue-devtools-stub

[github-actions-src]: https://img.shields.io/github/workflow/status/nuxt-contrib/vue-devtools-stub/ci/main?style=flat-square
[github-actions-href]: https://github.com/nuxt-contrib/vue-devtools-stub/actions?query=workflow%3Aci

[codecov-src]: https://img.shields.io/codecov/c/gh/nuxt-contrib/vue-devtools-stub/main?style=flat-square
[codecov-href]: https://codecov.io/gh/nuxt-contrib/vue-devtools-stub
