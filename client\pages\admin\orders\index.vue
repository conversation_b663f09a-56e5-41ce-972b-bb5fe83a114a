<template>
  <div>
    <!-- <PERSON>er -->
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">Orders</h1>
        <p class="mt-2 text-gray-600">Manage customer orders</p>
      </div>
    </div>

    <!-- Filters -->
    <UCard class="mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <UInput
          v-model="filters.search"
          placeholder="Search orders..."
          icon="i-heroicons-magnifying-glass"
        />

        <USelect
          v-model="filters.status"
          :options="statusOptions"
          placeholder="All Status"
        />

        <USelect
          v-model="filters.paymentStatus"
          :options="paymentStatusOptions"
          placeholder="All Payment Status"
        />

        <UButton @click="applyFilters" block> Apply Filters </UButton>
      </div>
    </UCard>

    <!-- Orders Table -->
    <UCard>
      <UTable
        :rows="orders"
        :columns="columns"
        :loading="isLoading"
        :empty-state="{
          icon: 'i-heroicons-shopping-bag',
          label: 'No orders found',
        }"
      >
        <template #orderNumber-data="{ row }">
          <div>
            <p class="font-medium text-gray-900">#{{ row.orderNumber }}</p>
            <p class="text-sm text-gray-500">{{ formatDate(row.createdAt) }}</p>
          </div>
        </template>

        <template #customer-data="{ row }">
          <div>
            <p class="font-medium text-gray-900">
              {{ row.user?.firstName }} {{ row.user?.lastName }}
            </p>
            <p class="text-sm text-gray-500">{{ row.user?.email }}</p>
          </div>
        </template>

        <template #total-data="{ row }">
          <p class="font-medium text-gray-900">${{ row.total }}</p>
        </template>

        <template #status-data="{ row }">
          <UBadge :color="getOrderStatusColor(row.status)" variant="soft">
            {{ row.status }}
          </UBadge>
        </template>

        <template #paymentStatus-data="{ row }">
          <UBadge
            :color="getPaymentStatusColor(row.paymentStatus)"
            variant="soft"
          >
            {{ row.paymentStatus }}
          </UBadge>
        </template>

        <template #actions-data="{ row }">
          <UDropdown :items="getActionItems(row)">
            <UButton variant="ghost" icon="i-heroicons-ellipsis-horizontal" />
          </UDropdown>
        </template>
      </UTable>

      <!-- Pagination -->
      <div class="flex justify-between items-center mt-6">
        <p class="text-sm text-gray-500">
          Showing {{ (currentPage - 1) * pageSize + 1 }} to
          {{ Math.min(currentPage * pageSize, totalOrders) }} of
          {{ totalOrders }} orders
        </p>

        <UPagination
          v-model="currentPage"
          :page-count="pageSize"
          :total="totalOrders"
          @update:model-value="fetchOrders"
        />
      </div>
    </UCard>

    <!-- Order Details Modal -->
    <UModal v-model="showOrderModal" :ui="{ width: 'max-w-4xl' }">
      <UCard v-if="selectedOrder">
        <template #header>
          <div class="flex justify-between items-center">
            <h3 class="text-lg font-semibold">
              Order #{{ selectedOrder.orderNumber }}
            </h3>
            <USelect
              v-model="selectedOrder.status"
              :options="statusSelectOptions"
              @change="updateOrderStatus"
            />
          </div>
        </template>

        <div class="space-y-6">
          <!-- Customer Info -->
          <div>
            <h4 class="font-medium text-gray-900 mb-2">Customer Information</h4>
            <div class="bg-gray-50 p-4 rounded-lg">
              <p>
                <strong>Name:</strong> {{ selectedOrder.user?.firstName }}
                {{ selectedOrder.user?.lastName }}
              </p>
              <p><strong>Email:</strong> {{ selectedOrder.user?.email }}</p>
            </div>
          </div>

          <!-- Order Items -->
          <div>
            <h4 class="font-medium text-gray-900 mb-2">Order Items</h4>
            <div class="space-y-2">
              <div
                v-for="item in selectedOrder.items"
                :key="item.id"
                class="flex justify-between items-center p-3 bg-gray-50 rounded-lg"
              >
                <div>
                  <p class="font-medium">{{ item.product?.name }}</p>
                  <p class="text-sm text-gray-500">
                    Quantity: {{ item.quantity }}
                  </p>
                </div>
                <p class="font-medium">${{ item.total }}</p>
              </div>
            </div>
          </div>

          <!-- Order Summary -->
          <div>
            <h4 class="font-medium text-gray-900 mb-2">Order Summary</h4>
            <div class="bg-gray-50 p-4 rounded-lg space-y-2">
              <div class="flex justify-between">
                <span>Subtotal:</span>
                <span>${{ selectedOrder.subtotal }}</span>
              </div>
              <div class="flex justify-between">
                <span>Tax:</span>
                <span>${{ selectedOrder.tax }}</span>
              </div>
              <div class="flex justify-between">
                <span>Shipping:</span>
                <span>${{ selectedOrder.shipping }}</span>
              </div>
              <div class="flex justify-between font-bold text-lg border-t pt-2">
                <span>Total:</span>
                <span>${{ selectedOrder.total }}</span>
              </div>
            </div>
          </div>
        </div>
      </UCard>
    </UModal>
  </div>
</template>

<script setup>
definePageMeta({
  layout: "admin-simple",
});

// Reactive data
const orders = ref([]);
const isLoading = ref(true);
const currentPage = ref(1);
const pageSize = ref(10);
const totalOrders = ref(0);
const showOrderModal = ref(false);
const selectedOrder = ref(null);

const filters = ref({
  search: "",
  status: "",
  paymentStatus: "",
});

// Table columns
const columns = [
  { key: "orderNumber", label: "Order" },
  { key: "customer", label: "Customer" },
  { key: "total", label: "Total" },
  { key: "status", label: "Status" },
  { key: "paymentStatus", label: "Payment" },
  { key: "actions", label: "Actions" },
];

// Filter options
const statusOptions = [
  { label: "All Status", value: "" },
  { label: "Pending", value: "PENDING" },
  { label: "Confirmed", value: "CONFIRMED" },
  { label: "Processing", value: "PROCESSING" },
  { label: "Shipped", value: "SHIPPED" },
  { label: "Delivered", value: "DELIVERED" },
  { label: "Cancelled", value: "CANCELLED" },
];

const paymentStatusOptions = [
  { label: "All Payment Status", value: "" },
  { label: "Pending", value: "PENDING" },
  { label: "Paid", value: "PAID" },
  { label: "Failed", value: "FAILED" },
  { label: "Refunded", value: "REFUNDED" },
];

const statusSelectOptions = [
  { label: "Pending", value: "PENDING" },
  { label: "Confirmed", value: "CONFIRMED" },
  { label: "Processing", value: "PROCESSING" },
  { label: "Shipped", value: "SHIPPED" },
  { label: "Delivered", value: "DELIVERED" },
  { label: "Cancelled", value: "CANCELLED" },
];

// Helper functions
const getOrderStatusColor = (status) => {
  const colors = {
    PENDING: "yellow",
    CONFIRMED: "blue",
    PROCESSING: "purple",
    SHIPPED: "orange",
    DELIVERED: "green",
    CANCELLED: "red",
  };
  return colors[status] || "gray";
};

const getPaymentStatusColor = (status) => {
  const colors = {
    PENDING: "yellow",
    PAID: "green",
    FAILED: "red",
    REFUNDED: "gray",
  };
  return colors[status] || "gray";
};

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString();
};

// Action items for dropdown
const getActionItems = (order) => [
  [
    {
      label: "View Details",
      icon: "i-heroicons-eye",
      click: () => viewOrder(order),
    },
  ],
];

// Fetch orders
const fetchOrders = async () => {
  try {
    isLoading.value = true;
    const { $api } = useNuxtApp();

    const params = new URLSearchParams({
      page: currentPage.value,
      limit: pageSize.value,
      ...filters.value,
    });

    const response = await $api(`/admin/orders?${params}`);
    orders.value = response.orders || [];
    totalOrders.value = response.total || 0;
  } catch (error) {
    console.error("Failed to fetch orders:", error);
  } finally {
    isLoading.value = false;
  }
};

// Apply filters
const applyFilters = () => {
  currentPage.value = 1;
  fetchOrders();
};

// View order details
const viewOrder = async (order) => {
  try {
    const { $api } = useNuxtApp();
    const response = await $api(`/admin/orders/${order.id}`);
    selectedOrder.value = response.order;
    showOrderModal.value = true;
  } catch (error) {
    console.error("Failed to fetch order details:", error);
  }
};

// Update order status
const updateOrderStatus = async () => {
  try {
    const { $api } = useNuxtApp();
    await $api(`/admin/orders/${selectedOrder.value.id}`, {
      method: "PUT",
      body: { status: selectedOrder.value.status },
    });
    await fetchOrders();
  } catch (error) {
    console.error("Failed to update order status:", error);
  }
};

// Load data on mount
onMounted(() => {
  fetchOrders();
});

// SEO
useHead({
  title: "Orders - Admin Panel",
});
</script>
