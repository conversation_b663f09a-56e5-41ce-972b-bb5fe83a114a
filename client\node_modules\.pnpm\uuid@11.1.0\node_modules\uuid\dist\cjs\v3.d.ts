import { UUIDTypes } from './types.js';
export { DNS, URL } from './v35.js';
declare function v3(value: string | Uint8Array, namespace: UUIDTypes, buf?: undefined, offset?: number): string;
declare function v3<TBuf extends Uint8Array = Uint8Array>(value: string | Uint8Array, namespace: UUIDTypes, buf: TBuf, offset?: number): TBuf;
declare namespace v3 {
    var DNS: string;
    var URL: string;
}
export default v3;
