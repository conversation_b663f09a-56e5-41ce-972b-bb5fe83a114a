<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Simple Admin Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo and Title -->
          <div class="flex items-center">
            <NuxtLink to="/admin" class="flex items-center space-x-3">
              <img src="/logo.svg" alt="Logo" class="h-8 w-auto" />
              <span class="text-xl font-bold text-gray-900">Admin Panel</span>
            </NuxtLink>
          </div>

          <!-- Simple User Menu -->
          <div class="flex items-center space-x-4">
            <NuxtLink
              to="/"
              class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
            >
              View Store
            </NuxtLink>
            
            <span class="text-gray-700">{{ authStore.fullName }}</span>
            
            <button
              @click="authStore.logout()"
              class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
            >
              Logout
            </button>
          </div>
        </div>
      </div>
    </header>

    <div class="flex">
      <!-- Simple Sidebar -->
      <aside class="w-64 bg-white shadow-sm min-h-screen">
        <nav class="mt-8 px-4">
          <div class="space-y-2">
            <NuxtLink
              to="/admin"
              class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors"
              :class="[
                $route.path === '/admin'
                  ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                  : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
              ]"
            >
              📊 Dashboard
            </NuxtLink>
            
            <NuxtLink
              to="/admin/products"
              class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors"
              :class="[
                $route.path.startsWith('/admin/products')
                  ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                  : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
              ]"
            >
              📦 Products
            </NuxtLink>
            
            <NuxtLink
              to="/admin/categories"
              class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors"
              :class="[
                $route.path.startsWith('/admin/categories')
                  ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                  : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
              ]"
            >
              🏷️ Categories
            </NuxtLink>
            
            <NuxtLink
              to="/admin/orders"
              class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors"
              :class="[
                $route.path.startsWith('/admin/orders')
                  ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                  : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
              ]"
            >
              🛒 Orders
            </NuxtLink>
            
            <NuxtLink
              to="/admin/settings"
              class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors"
              :class="[
                $route.path.startsWith('/admin/settings')
                  ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                  : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
              ]"
            >
              ⚙️ Settings
            </NuxtLink>
          </div>
        </nav>
      </aside>

      <!-- Main Content -->
      <main class="flex-1 p-8">
        <slot />
      </main>
    </div>
  </div>
</template>

<script setup>
const authStore = useAuthStore()
const router = useRouter()

// Check if user is admin
onMounted(() => {
  if (!authStore.user || authStore.user.role !== 'ADMIN') {
    router.push('/login')
  }
})

// SEO
useHead({
  title: 'Admin Panel - Ecommerce Store'
})
</script>
