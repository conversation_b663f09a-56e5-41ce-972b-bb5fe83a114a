{"name": "ecommerce-server", "version": "1.0.0", "description": "Express server for ecommerce webstore", "main": "src/index.js", "scripts": {"dev": "nodemon src/index.js", "start": "node src/index.js", "build": "echo 'No build step needed for Express'", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "node src/seed.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "stripe": "^14.5.0", "redis": "^4.6.10", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "@prisma/client": "^5.6.0", "prisma": "^5.6.0"}, "devDependencies": {"nodemon": "^3.0.1"}}