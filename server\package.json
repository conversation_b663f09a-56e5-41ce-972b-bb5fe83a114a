{"name": "ecommerce-server", "version": "1.0.0", "description": "Express server for ecommerce webstore", "main": "src/index.js", "scripts": {"dev": "nodemon src/index.js", "start": "node src/index.js", "build": "echo 'No build step needed for Express'", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "node src/seed.js"}, "dependencies": {"@prisma/client": "^6.9.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "redis": "^4.6.10", "stripe": "^14.5.0"}, "devDependencies": {"nodemon": "^3.0.1", "prisma": "^6.9.0"}}